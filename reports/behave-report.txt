Feature: Enhanced Risk Modeling and Monte Carlo Simulations # features/enhanced_risk_modeling.feature:1
  As a security analyst
  I want to perform advanced risk calculations with Monte Carlo simulations
  So that I can make data-driven investment decisions with confidence intervals
  Background:   # features/enhanced_risk_modeling.feature:6

  @risk_profile
  Scenario: Create a healthcare organization risk profile                                      # features/enhanced_risk_modeling.feature:11
    Given I am a logged-in security analyst                                                    # features/steps/enhanced_risk_steps.py:29
    And I have access to the enhanced risk modeling features                                   # features/steps/enhanced_risk_steps.py:36
    Given I want to create a risk profile for my organization                                  # features/steps/enhanced_risk_steps.py:45
    When I provide the following organization details                                          # features/steps/enhanced_risk_steps.py:51
      | field                     | value                    |
      | name                      | Regional Healthcare Corp |
      | industry                  | healthcare               |
      | organization_size         | medium                   |
      | annual_revenue            | 50000000                 |
      | employee_count            | 1200                     |
      | data_sensitivity_level    | 5                        |
      | regulatory_requirements   | HIPAA,SOC2               |
      | previous_incidents        | 2                        |
      | current_security_maturity | 3                        |
      | business_criticality      | high                     |
    Then the risk profile should be created successfully                                       # features/steps/enhanced_risk_steps.py:68
    And the breach cost multiplier should be calculated based on healthcare industry standards # features/steps/enhanced_risk_steps.py:85
    And the breach probability should reflect the organization's security maturity             # features/steps/enhanced_risk_steps.py:96

  @risk_profile
  Scenario: Validate industry-specific breach cost calculations                    # features/enhanced_risk_modeling.feature:30
    Given I am a logged-in security analyst                                        # features/steps/enhanced_risk_steps.py:29
    And I have access to the enhanced risk modeling features                       # features/steps/enhanced_risk_steps.py:36
    Given I have a risk profile for a "healthcare" organization with "medium" size # features/steps/enhanced_risk_steps.py:132
    When the system calculates the breach cost multiplier                          # features/steps/enhanced_risk_steps.py:139
    Then the multiplier should be higher than the financial industry average       # features/steps/enhanced_risk_steps.py:146
    And the calculation should include regulatory compliance factors               # features/steps/enhanced_risk_steps.py:154
    And the result should be based on IBM Cost of Breach study data                # None

  @risk_profile
  Scenario: Reject invalid organization size and employee count combination    # features/enhanced_risk_modeling.feature:38
    Given I am a logged-in security analyst                                    # features/steps/enhanced_risk_steps.py:29
    And I have access to the enhanced risk modeling features                   # features/steps/enhanced_risk_steps.py:36
    Given I want to create a risk profile                                      # features/steps/enhanced_risk_steps.py:177
    When I specify organization size as "small" but employee count as "5000"   # features/steps/enhanced_risk_steps.py:190
    Then the system should reject the profile creation                         # None
    And I should receive a validation error about mismatched organization size # None

  @monte_carlo
  Scenario: Perform Monte Carlo simulation for security investment           # features/enhanced_risk_modeling.feature:45
    Given I am a logged-in security analyst                                  # None
    And I have access to the enhanced risk modeling features                 # None
    Given I have a base ROI calculation for a security tool investment       # None
    And I have a healthcare risk profile                                     # None
    When I create an enhanced risk calculation with the following parameters # None
      | parameter_name        | distribution_type | mean | std  | min     | max      |
      | breach_cost           | lognormal         | 15.4 | 0.5  | 1000000 | 50000000 |
      | breach_probability    | beta              | 2    | 5    | 0.05    | 0.95     |
      | risk_reduction_factor | triangular        | 0.05 | 0.15 | 0.35    |          |
    And I set simulation iterations to "10000"                               # None
    Then the Monte Carlo simulation should complete successfully             # None
    And I should receive enhanced ROI calculations                           # None
    And the results should include confidence intervals                      # None
    And Value at Risk metrics should be calculated                           # None

  @monte_carlo
  Scenario: Generate confidence intervals for investment analysis                # features/enhanced_risk_modeling.feature:60
    Given I am a logged-in security analyst                                      # None
    And I have access to the enhanced risk modeling features                     # None
    Given I have completed a Monte Carlo simulation with 10000 iterations        # None
    When I request the calculation results                                       # None
    Then I should receive confidence intervals at the following percentiles      # None
      | percentile | description    |
      | 5          | Worst case     |
      | 25         | Lower quartile |
      | 50         | Median         |
      | 75         | Upper quartile |
      | 95         | Best case      |
    And each interval should have realistic values based on the input parameters # None

  @monte_carlo
  Scenario: Calculate Value at Risk for security investment          # features/enhanced_risk_modeling.feature:73
    Given I am a logged-in security analyst                          # None
    And I have access to the enhanced risk modeling features         # None
    Given I have completed an enhanced risk calculation              # None
    When I analyze the Value at Risk metrics                         # None
    Then I should receive VaR at 95% confidence level                # None
    And I should receive VaR at 99% confidence level                 # None
    And I should receive Conditional VaR (Expected Shortfall) at 95% # None
    And all VaR values should be within reasonable bounds            # None

  @sensitivity_analysis
  Scenario: Perform sensitivity analysis on risk parameters            # features/enhanced_risk_modeling.feature:82
    Given I am a logged-in security analyst                            # None
    And I have access to the enhanced risk modeling features           # None
    Given I have completed a Monte Carlo simulation                    # None
    When I request sensitivity analysis results                        # None
    Then I should receive parameter sensitivity rankings               # None
    And the most influential parameters should be identified           # None
    And correlation coefficients should be provided for each parameter # None
    And tornado chart data should be available for visualization       # None

  @business_logic
  Scenario: Healthcare organization gets appropriate risk multipliers         # features/enhanced_risk_modeling.feature:91
    Given I am a logged-in security analyst                                   # None
    And I have access to the enhanced risk modeling features                  # None
    Given I have a healthcare organization risk profile                       # None
    When the system calculates risk metrics                                   # None
    Then the breach cost should reflect healthcare industry premiums          # None
    And HIPAA compliance requirements should increase the multiplier          # None
    And the breach probability should account for healthcare-specific threats # None

  @business_logic
  Scenario: Security maturity reduces breach probability                                     # features/enhanced_risk_modeling.feature:99
    Given I am a logged-in security analyst                                                  # None
    And I have access to the enhanced risk modeling features                                 # None
    Given I have two identical organization profiles                                         # None
    When one has security maturity level "2" and the other has level "4"                     # None
    Then the organization with higher security maturity should have lower breach probability # None
    And the difference should be approximately 10% (2 levels × 5% per level)                 # None

  @business_logic
  Scenario: Previous incidents increase risk assessment                       # features/enhanced_risk_modeling.feature:106
    Given I am a logged-in security analyst                                   # None
    And I have access to the enhanced risk modeling features                  # None
    Given I have an organization profile with "3" previous security incidents # None
    When the system calculates breach probability                             # None
    Then the probability should be increased by approximately 9% (3 × 3%)     # None
    But the increase should not exceed 15% maximum                            # None

  @integration
  Scenario: Enhanced calculation integrates with base ROI calculation    # features/enhanced_risk_modeling.feature:113
    Given I am a logged-in security analyst                              # None
    And I have access to the enhanced risk modeling features             # None
    Given I have a base ROI calculation showing 150% ROI                 # None
    And I have a risk profile for a technology company                   # None
    When I create an enhanced risk calculation                           # None
    Then the enhanced ROI should incorporate risk-adjusted values        # None
    And the result should show both optimistic and pessimistic scenarios # None
    And the risk-adjusted ROI should be different from the base ROI      # None

  @validation
  Scenario: Reject Monte Carlo simulation with insufficient iterations  # features/enhanced_risk_modeling.feature:122
    Given I am a logged-in security analyst                             # None
    And I have access to the enhanced risk modeling features            # None
    Given I want to create an enhanced risk calculation                 # None
    When I specify simulation iterations as "500"                       # None
    Then the system should reject the request                           # None
    And I should receive an error about minimum iteration requirements  # None

  @validation
  Scenario: Validate probability distribution parameters                          # features/enhanced_risk_modeling.feature:129
    Given I am a logged-in security analyst                                       # None
    And I have access to the enhanced risk modeling features                      # None
    Given I want to create Monte Carlo parameters                                 # None
    When I specify a "normal" distribution without required "std" parameter       # None
    Then the system should reject the parameter configuration                     # None
    And I should receive a validation error about missing distribution parameters # None

  @performance
  Scenario: Monte Carlo simulation completes within reasonable time  # features/enhanced_risk_modeling.feature:136
    Given I am a logged-in security analyst                          # None
    And I have access to the enhanced risk modeling features         # None
    Given I have a valid enhanced risk calculation setup             # None
    When I run a simulation with 10000 iterations                    # None
    Then the calculation should complete within 30 seconds           # None
    And the system should report the calculation duration            # None
    And convergence should be achieved within the iteration limit    # None

  @reporting
  Scenario: Generate comprehensive risk calculation summary  # features/enhanced_risk_modeling.feature:144
    Given I am a logged-in security analyst                  # None
    And I have access to the enhanced risk modeling features # None
    Given I have completed an enhanced risk calculation      # None
    When I request the calculation summary                   # None
    Then I should receive a summary containing               # None
      | metric                  | included |
      | Enhanced ROI percentage | yes      |
      | Risk-adjusted value     | yes      |
      | Expected annual loss    | yes      |
      | VaR at 95%              | yes      |
      | VaR at 99%              | yes      |
      | Confidence intervals    | yes      |
      | Calculation duration    | yes      |
      | Iteration count         | yes      |

  @data_persistence
  Scenario: Risk profiles are saved and retrievable                     # features/enhanced_risk_modeling.feature:159
    Given I am a logged-in security analyst                             # None
    And I have access to the enhanced risk modeling features            # None
    Given I have created a risk profile named "Test Healthcare Profile" # None
    When I list my risk profiles                                        # None
    Then I should see "Test Healthcare Profile" in the list             # None
    And I should be able to retrieve the full profile details           # None
    And all the original parameters should be preserved                 # None

  @data_persistence
  Scenario: Enhanced calculations are linked to base calculations  # features/enhanced_risk_modeling.feature:167
    Given I am a logged-in security analyst                        # None
    And I have access to the enhanced risk modeling features       # None
    Given I have a base calculation with ID "123"                  # None
    And I have created an enhanced calculation based on it         # None
    When I retrieve the enhanced calculation                       # None
    Then it should reference base calculation ID "123"             # None
    And I should be able to access both calculations independently # None

  @access_control
  Scenario: Users can only access their own risk profiles    # features/enhanced_risk_modeling.feature:175
    Given I am a logged-in security analyst                  # None
    And I have access to the enhanced risk modeling features # None
    Given I have created a private risk profile              # None
    And another user exists in the system                    # None
    When the other user tries to access my risk profile      # None
    Then they should receive an access denied error          # None
    And my profile should remain private                     # None

  @access_control
  Scenario: Public risk profiles are accessible to all users  # features/enhanced_risk_modeling.feature:183
    Given I am a logged-in security analyst                   # None
    And I have access to the enhanced risk modeling features  # None
    Given I have created a public risk profile template       # None
    When another user lists available risk profiles           # None
    Then they should see my public profile in their list      # None
    And they should be able to use it as a template           # None

  @error_handling
  Scenario: Handle missing base calculation gracefully                    # features/enhanced_risk_modeling.feature:190
    Given I am a logged-in security analyst                               # None
    And I have access to the enhanced risk modeling features              # None
    Given I want to create an enhanced risk calculation                   # None
    When I reference a non-existent base calculation ID "999"             # None
    Then the system should return a "not found" error                     # None
    And the error message should clearly indicate the missing calculation # None

  @error_handling
  Scenario: Handle invalid risk profile reference                          # features/enhanced_risk_modeling.feature:197
    Given I am a logged-in security analyst                                # None
    And I have access to the enhanced risk modeling features               # None
    Given I want to create an enhanced risk calculation                    # None
    When I reference a non-existent risk profile ID "888"                  # None
    Then the system should return a "not found" error                      # None
    And the error message should clearly indicate the missing risk profile # None

