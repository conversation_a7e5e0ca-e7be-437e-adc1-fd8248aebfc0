Feature: API Health and Connectivity # features/api_health.feature:1
  As a developer
  I want to verify the API is running and accessible
  So that I can run comprehensive BDD tests
  @health_check
  Scenario: API health check responds successfully       # features/api_health.feature:7
    Given the CSO platform is running                    # features/steps/user_management_steps.py:17
    When I check the API health endpoint                 # features/steps/api_health_steps.py:18
    Then the health check should return status "healthy" # features/steps/api_health_steps.py:25
    And the response should include service information  # features/steps/api_health_steps.py:34
    And the response should include version information  # features/steps/api_health_steps.py:42

  @api_docs
  Scenario: API documentation is accessible       # features/api_health.feature:15
    Given the CSO platform is running             # features/steps/user_management_steps.py:17
    When I access the API documentation           # features/steps/api_health_steps.py:50
    Then the documentation should be available    # features/steps/api_health_steps.py:57
    And it should include API version information # features/steps/api_health_steps.py:63

  @service_url_manager
  Scenario: Service URL Manager integration works                  # features/api_health.feature:22
    Given the CSO platform is running                              # features/steps/user_management_steps.py:17
    When I test the Service URL Manager configuration              # features/steps/api_health_steps.py:71
    Then it should generate correct URLs for the local environment # features/steps/api_health_steps.py:89
    And it should handle different service types correctly         # features/steps/api_health_steps.py:101

Feature: Enhanced Risk Modeling and Monte Carlo Simulations # features/enhanced_risk_modeling.feature:1
  As a security analyst
  I want to perform advanced risk calculations with Monte Carlo simulations
  So that I can make data-driven investment decisions with confidence intervals
  Background:   # features/enhanced_risk_modeling.feature:6

  @risk_profile
  Scenario: Create a healthcare organization risk profile                                      # features/enhanced_risk_modeling.feature:11
    Given I am a logged-in security analyst                                                    # None
    And I have access to the enhanced risk modeling features                                   # None
    Given I want to create a risk profile for my organization                                  # None
    When I provide the following organization details                                          # None
      | field                     | value                    |
      | name                      | Regional Healthcare Corp |
      | industry                  | healthcare               |
      | organization_size         | medium                   |
      | annual_revenue            | 50000000                 |
      | employee_count            | 1200                     |
      | data_sensitivity_level    | 5                        |
      | regulatory_requirements   | HIPAA,SOC2               |
      | previous_incidents        | 2                        |
      | current_security_maturity | 3                        |
      | business_criticality      | high                     |
    Then the risk profile should be created successfully                                       # None
    And the breach cost multiplier should be calculated based on healthcare industry standards # None
    And the breach probability should reflect the organization's security maturity             # None

  @risk_profile
  Scenario: Validate industry-specific breach cost calculations                    # features/enhanced_risk_modeling.feature:30
    Given I am a logged-in security analyst                                        # None
    And I have access to the enhanced risk modeling features                       # None
    Given I have a risk profile for a "healthcare" organization with "medium" size # None
    When the system calculates the breach cost multiplier                          # None
    Then the multiplier should be higher than the financial industry average       # None
    And the calculation should include regulatory compliance factors               # None
    And the result should be based on IBM Cost of Breach study data                # None

  @risk_profile
  Scenario: Reject invalid organization size and employee count combination    # features/enhanced_risk_modeling.feature:38
    Given I am a logged-in security analyst                                    # None
    And I have access to the enhanced risk modeling features                   # None
    Given I want to create a risk profile                                      # None
    When I specify organization size as "small" but employee count as "5000"   # None
    Then the system should reject the profile creation                         # None
    And I should receive a validation error about mismatched organization size # None

  @monte_carlo
  Scenario: Perform Monte Carlo simulation for security investment           # features/enhanced_risk_modeling.feature:45
    Given I am a logged-in security analyst                                  # None
    And I have access to the enhanced risk modeling features                 # None
    Given I have a base ROI calculation for a security tool investment       # None
    And I have a healthcare risk profile                                     # None
    When I create an enhanced risk calculation with the following parameters # None
      | parameter_name        | distribution_type | mean | std  | min     | max      |
      | breach_cost           | lognormal         | 15.4 | 0.5  | 1000000 | 50000000 |
      | breach_probability    | beta              | 2    | 5    | 0.05    | 0.95     |
      | risk_reduction_factor | triangular        | 0.05 | 0.15 | 0.35    |          |
    And I set simulation iterations to "10000"                               # None
    Then the Monte Carlo simulation should complete successfully             # None
    And I should receive enhanced ROI calculations                           # None
    And the results should include confidence intervals                      # None
    And Value at Risk metrics should be calculated                           # None

  @monte_carlo
  Scenario: Generate confidence intervals for investment analysis                # features/enhanced_risk_modeling.feature:60
    Given I am a logged-in security analyst                                      # None
    And I have access to the enhanced risk modeling features                     # None
    Given I have completed a Monte Carlo simulation with 10000 iterations        # None
    When I request the calculation results                                       # None
    Then I should receive confidence intervals at the following percentiles      # None
      | percentile | description    |
      | 5          | Worst case     |
      | 25         | Lower quartile |
      | 50         | Median         |
      | 75         | Upper quartile |
      | 95         | Best case      |
    And each interval should have realistic values based on the input parameters # None

  @monte_carlo
  Scenario: Calculate Value at Risk for security investment          # features/enhanced_risk_modeling.feature:73
    Given I am a logged-in security analyst                          # None
    And I have access to the enhanced risk modeling features         # None
    Given I have completed an enhanced risk calculation              # None
    When I analyze the Value at Risk metrics                         # None
    Then I should receive VaR at 95% confidence level                # None
    And I should receive VaR at 99% confidence level                 # None
    And I should receive Conditional VaR (Expected Shortfall) at 95% # None
    And all VaR values should be within reasonable bounds            # None

  @sensitivity_analysis
  Scenario: Perform sensitivity analysis on risk parameters            # features/enhanced_risk_modeling.feature:82
    Given I am a logged-in security analyst                            # None
    And I have access to the enhanced risk modeling features           # None
    Given I have completed a Monte Carlo simulation                    # None
    When I request sensitivity analysis results                        # None
    Then I should receive parameter sensitivity rankings               # None
    And the most influential parameters should be identified           # None
    And correlation coefficients should be provided for each parameter # None
    And tornado chart data should be available for visualization       # None

  @business_logic
  Scenario: Healthcare organization gets appropriate risk multipliers         # features/enhanced_risk_modeling.feature:91
    Given I am a logged-in security analyst                                   # None
    And I have access to the enhanced risk modeling features                  # None
    Given I have a healthcare organization risk profile                       # None
    When the system calculates risk metrics                                   # None
    Then the breach cost should reflect healthcare industry premiums          # None
    And HIPAA compliance requirements should increase the multiplier          # None
    And the breach probability should account for healthcare-specific threats # None

  @business_logic
  Scenario: Security maturity reduces breach probability                                     # features/enhanced_risk_modeling.feature:99
    Given I am a logged-in security analyst                                                  # None
    And I have access to the enhanced risk modeling features                                 # None
    Given I have two identical organization profiles                                         # None
    When one has security maturity level "2" and the other has level "4"                     # None
    Then the organization with higher security maturity should have lower breach probability # None
    And the difference should be approximately 10% (2 levels × 5% per level)                 # None

  @business_logic
  Scenario: Previous incidents increase risk assessment                       # features/enhanced_risk_modeling.feature:106
    Given I am a logged-in security analyst                                   # None
    And I have access to the enhanced risk modeling features                  # None
    Given I have an organization profile with "3" previous security incidents # None
    When the system calculates breach probability                             # None
    Then the probability should be increased by approximately 9% (3 × 3%)     # None
    But the increase should not exceed 15% maximum                            # None

  @integration
  Scenario: Enhanced calculation integrates with base ROI calculation    # features/enhanced_risk_modeling.feature:113
    Given I am a logged-in security analyst                              # None
    And I have access to the enhanced risk modeling features             # None
    Given I have a base ROI calculation showing 150% ROI                 # None
    And I have a risk profile for a technology company                   # None
    When I create an enhanced risk calculation                           # None
    Then the enhanced ROI should incorporate risk-adjusted values        # None
    And the result should show both optimistic and pessimistic scenarios # None
    And the risk-adjusted ROI should be different from the base ROI      # None

  @validation
  Scenario: Reject Monte Carlo simulation with insufficient iterations                           # features/enhanced_risk_modeling.feature:122
    Given I am a logged-in security analyst                                                      # None
    And I have access to the enhanced risk modeling features                                     # None
    Given I want to create an enhanced risk calculation                                          # None
    When I specify simulation iterations as "500"                                                # None
    Then the system should reject the request                                                    # None
    And I should receive an error about minimum iteration requirements for enhanced calculations # None

  @validation
  Scenario: Validate probability distribution parameters                          # features/enhanced_risk_modeling.feature:129
    Given I am a logged-in security analyst                                       # None
    And I have access to the enhanced risk modeling features                      # None
    Given I want to create Monte Carlo parameters                                 # None
    When I specify a "normal" distribution without required "std" parameter       # None
    Then the system should reject the parameter configuration                     # None
    And I should receive a validation error about missing distribution parameters # None

  @performance
  Scenario: Monte Carlo simulation completes within reasonable time  # features/enhanced_risk_modeling.feature:136
    Given I am a logged-in security analyst                          # None
    And I have access to the enhanced risk modeling features         # None
    Given I have a valid enhanced risk calculation setup             # None
    When I run a simulation with 10000 iterations                    # None
    Then the calculation should complete within 30 seconds           # None
    And the system should report the calculation duration            # None
    And convergence should be achieved within the iteration limit    # None

  @reporting
  Scenario: Generate comprehensive risk calculation summary  # features/enhanced_risk_modeling.feature:144
    Given I am a logged-in security analyst                  # None
    And I have access to the enhanced risk modeling features # None
    Given I have completed an enhanced risk calculation      # None
    When I request the calculation summary                   # None
    Then I should receive a summary containing               # None
      | metric                  | included |
      | Enhanced ROI percentage | yes      |
      | Risk-adjusted value     | yes      |
      | Expected annual loss    | yes      |
      | VaR at 95%              | yes      |
      | VaR at 99%              | yes      |
      | Confidence intervals    | yes      |
      | Calculation duration    | yes      |
      | Iteration count         | yes      |

  @data_persistence
  Scenario: Risk profiles are saved and retrievable                     # features/enhanced_risk_modeling.feature:159
    Given I am a logged-in security analyst                             # None
    And I have access to the enhanced risk modeling features            # None
    Given I have created a risk profile named "Test Healthcare Profile" # None
    When I list my risk profiles                                        # None
    Then I should see "Test Healthcare Profile" in the list             # None
    And I should be able to retrieve the full profile details           # None
    And all the original parameters should be preserved                 # None

  @data_persistence
  Scenario: Enhanced calculations are linked to base calculations  # features/enhanced_risk_modeling.feature:167
    Given I am a logged-in security analyst                        # None
    And I have access to the enhanced risk modeling features       # None
    Given I have a base calculation with ID "123"                  # None
    And I have created an enhanced calculation based on it         # None
    When I retrieve the enhanced calculation                       # None
    Then it should reference base calculation ID "123"             # None
    And I should be able to access both calculations independently # None

  @access_control
  Scenario: Users can only access their own risk profiles    # features/enhanced_risk_modeling.feature:175
    Given I am a logged-in security analyst                  # None
    And I have access to the enhanced risk modeling features # None
    Given I have created a private risk profile              # None
    And another user exists in the system                    # None
    When the other user tries to access my risk profile      # None
    Then they should receive an access denied error          # None
    And my profile should remain private                     # None

  @access_control
  Scenario: Public risk profiles are accessible to all users  # features/enhanced_risk_modeling.feature:183
    Given I am a logged-in security analyst                   # None
    And I have access to the enhanced risk modeling features  # None
    Given I have created a public risk profile template       # None
    When another user lists available risk profiles           # None
    Then they should see my public profile in their list      # None
    And they should be able to use it as a template           # None

  @error_handling
  Scenario: Handle missing base calculation gracefully                    # features/enhanced_risk_modeling.feature:190
    Given I am a logged-in security analyst                               # None
    And I have access to the enhanced risk modeling features              # None
    Given I want to create an enhanced risk calculation                   # None
    When I reference a non-existent base calculation ID "999"             # None
    Then the system should return a "not found" error                     # None
    And the error message should clearly indicate the missing calculation # None

  @error_handling
  Scenario: Handle invalid risk profile reference                          # features/enhanced_risk_modeling.feature:197
    Given I am a logged-in security analyst                                # None
    And I have access to the enhanced risk modeling features               # None
    Given I want to create an enhanced risk calculation                    # None
    When I reference a non-existent risk profile ID "888"                  # None
    Then the system should return a "not found" error                      # None
    And the error message should clearly indicate the missing risk profile # None

Feature: Monte Carlo Simulations for Risk Analysis # features/monte_carlo_simulations.feature:1
  As a security analyst
  I want to perform Monte Carlo simulations for risk calculations
  So that I can understand the uncertainty and variability in my security investment decisions
  Background:   # features/monte_carlo_simulations.feature:6

  @monte_carlo_basic
  Scenario: Create basic Monte Carlo simulation                          # features/monte_carlo_simulations.feature:11
    Given I am logged in as a security analyst                           # None
    And I have access to Monte Carlo simulation features                 # None
    Given I have a base ROI calculation                                  # None
    And I have a risk profile for my organization                        # None
    When I create a Monte Carlo simulation with the following parameters # None
      | parameter_name     | distribution | mean | std  | min     | max      |
      | breach_cost        | lognormal    | 15.4 | 0.5  | 1000000 | 50000000 |
      | breach_probability | beta         | 2    | 5    | 0.05    | 0.95     |
      | risk_reduction     | triangular   | 0.15 | 0.25 | 0.35    |          |
    And I set the simulation to run 10000 iterations                     # None
    Then the simulation should complete successfully                     # None
    And I should receive statistical results                             # None
    And the simulation results should include confidence intervals       # None
    And the simulation should converge within the iteration limit        # None

  @monte_carlo_distributions
  Scenario Outline: Test different probability distributions -- @1.1   # features/monte_carlo_simulations.feature:35
    Given I am logged in as a security analyst                         # None
    And I have access to Monte Carlo simulation features               # None
    Given I have a Monte Carlo simulation setup                        # None
    When I configure a parameter with "normal" distribution            # None
    And I provide the required parameters for "normal"                 # None
    Then the parameter should be validated successfully                # None
    And the distribution should generate valid samples                 # None

  @monte_carlo_distributions
  Scenario Outline: Test different probability distributions -- @1.2   # features/monte_carlo_simulations.feature:36
    Given I am logged in as a security analyst                         # None
    And I have access to Monte Carlo simulation features               # None
    Given I have a Monte Carlo simulation setup                        # None
    When I configure a parameter with "lognormal" distribution         # None
    And I provide the required parameters for "lognormal"              # None
    Then the parameter should be validated successfully                # None
    And the distribution should generate valid samples                 # None

  @monte_carlo_distributions
  Scenario Outline: Test different probability distributions -- @1.3   # features/monte_carlo_simulations.feature:37
    Given I am logged in as a security analyst                         # None
    And I have access to Monte Carlo simulation features               # None
    Given I have a Monte Carlo simulation setup                        # None
    When I configure a parameter with "beta" distribution              # None
    And I provide the required parameters for "beta"                   # None
    Then the parameter should be validated successfully                # None
    And the distribution should generate valid samples                 # None

  @monte_carlo_distributions
  Scenario Outline: Test different probability distributions -- @1.4   # features/monte_carlo_simulations.feature:38
    Given I am logged in as a security analyst                         # None
    And I have access to Monte Carlo simulation features               # None
    Given I have a Monte Carlo simulation setup                        # None
    When I configure a parameter with "triangular" distribution        # None
    And I provide the required parameters for "triangular"             # None
    Then the parameter should be validated successfully                # None
    And the distribution should generate valid samples                 # None

  @monte_carlo_distributions
  Scenario Outline: Test different probability distributions -- @1.5   # features/monte_carlo_simulations.feature:39
    Given I am logged in as a security analyst                         # None
    And I have access to Monte Carlo simulation features               # None
    Given I have a Monte Carlo simulation setup                        # None
    When I configure a parameter with "uniform" distribution           # None
    And I provide the required parameters for "uniform"                # None
    Then the parameter should be validated successfully                # None
    And the distribution should generate valid samples                 # None

  @monte_carlo_validation
  Scenario: Reject simulation with insufficient iterations             # features/monte_carlo_simulations.feature:42
    Given I am logged in as a security analyst                         # None
    And I have access to Monte Carlo simulation features               # None
    Given I have a Monte Carlo simulation setup                        # None
    When I set the iteration count to 500                              # None
    Then the simulation should be rejected                             # None
    And I should receive an error about minimum iteration requirements # None
    And the minimum should be 1000 iterations                          # None

  @monte_carlo_validation
  Scenario: Validate distribution parameters                                 # features/monte_carlo_simulations.feature:50
    Given I am logged in as a security analyst                               # None
    And I have access to Monte Carlo simulation features                     # None
    Given I want to create a Monte Carlo parameter                           # None
    When I specify a "normal" distribution without "std" parameter           # None
    Then the parameter should be rejected                                    # None
    And I should receive a validation error about missing standard deviation # None

  @monte_carlo_performance
  Scenario: Large simulation completes within time limit        # features/monte_carlo_simulations.feature:57
    Given I am logged in as a security analyst                  # None
    And I have access to Monte Carlo simulation features        # None
    Given I have a Monte Carlo simulation with 50000 iterations # None
    When I run the simulation                                   # None
    Then it should complete within 60 seconds                   # None
    And the results should be statistically valid               # None
    And convergence should be achieved                          # None

  @monte_carlo_convergence
  Scenario: Simulation achieves convergence              # features/monte_carlo_simulations.feature:65
    Given I am logged in as a security analyst           # None
    And I have access to Monte Carlo simulation features # None
    Given I have a Monte Carlo simulation running        # None
    When the simulation reaches convergence criteria     # None
    Then the simulation should stop automatically        # None
    And I should receive a convergence notification      # None
    And the final iteration count should be reported     # None

  @monte_carlo_sensitivity
  Scenario: Perform sensitivity analysis                     # features/monte_carlo_simulations.feature:73
    Given I am logged in as a security analyst               # None
    And I have access to Monte Carlo simulation features     # None
    Given I have completed a Monte Carlo simulation          # None
    When I request sensitivity analysis                      # None
    Then I should receive parameter sensitivity rankings     # None
    And the most influential parameters should be identified # None
    And correlation coefficients should be provided          # None
    And tornado chart data should be available               # None

  @monte_carlo_var
  Scenario: Calculate Value at Risk metrics                   # features/monte_carlo_simulations.feature:82
    Given I am logged in as a security analyst                # None
    And I have access to Monte Carlo simulation features      # None
    Given I have completed a Monte Carlo simulation           # None
    When I analyze the risk metrics                           # None
    Then I should receive VaR at 95% confidence level         # None
    And I should receive VaR at 99% confidence level          # None
    And I should receive Conditional VaR (Expected Shortfall) # None
    And all VaR values should be mathematically consistent    # None

  @monte_carlo_confidence_intervals
  Scenario: Generate confidence intervals                       # features/monte_carlo_simulations.feature:91
    Given I am logged in as a security analyst                  # None
    And I have access to Monte Carlo simulation features        # None
    Given I have completed a Monte Carlo simulation             # None
    When I request confidence interval analysis                 # None
    Then I should receive intervals at key percentiles          # None
      | percentile | description    |
      | 5          | Worst case     |
      | 25         | Lower quartile |
      | 50         | Median         |
      | 75         | Upper quartile |
      | 95         | Best case      |
    And the intervals should be in ascending order              # None
    And the values should be realistic for the input parameters # None

  @monte_carlo_random_seed
  Scenario: Reproducible results with random seed             # features/monte_carlo_simulations.feature:105
    Given I am logged in as a security analyst                # None
    And I have access to Monte Carlo simulation features      # None
    Given I have a Monte Carlo simulation with random seed 42 # None
    When I run the simulation twice with the same seed        # None
    Then both runs should produce identical results           # None
    And the statistical measures should match exactly         # None

  @monte_carlo_error_handling
  Scenario: Handle invalid parameter combinations                  # features/monte_carlo_simulations.feature:112
    Given I am logged in as a security analyst                     # None
    And I have access to Monte Carlo simulation features           # None
    Given I want to create a Monte Carlo simulation                # None
    When I provide parameters that would cause mathematical errors # None
    Then the simulation should detect the invalid configuration    # None
    And I should receive a descriptive error message               # None
    And the simulation should not start                            # None

  @monte_carlo_memory
  Scenario: Handle large simulations efficiently                  # features/monte_carlo_simulations.feature:120
    Given I am logged in as a security analyst                    # None
    And I have access to Monte Carlo simulation features          # None
    Given I have a Monte Carlo simulation with 100000 iterations  # None
    When I run the simulation                                     # None
    Then the memory usage should remain within acceptable limits  # None
    And the simulation should not cause system performance issues # None
    And results should be processed incrementally                 # None

  @monte_carlo_parallel
  Scenario: Parallel processing for large simulations            # features/monte_carlo_simulations.feature:128
    Given I am logged in as a security analyst                   # None
    And I have access to Monte Carlo simulation features         # None
    Given I have a Monte Carlo simulation with 50000 iterations  # None
    When the simulation runs with parallel processing enabled    # None
    Then it should complete faster than sequential processing    # None
    And the results should be identical to sequential processing # None
    And all CPU cores should be utilized efficiently             # None

  @monte_carlo_export
  Scenario: Export simulation results                      # features/monte_carlo_simulations.feature:136
    Given I am logged in as a security analyst             # None
    And I have access to Monte Carlo simulation features   # None
    Given I have completed a Monte Carlo simulation        # None
    When I request to export the results                   # None
    Then I should be able to export in multiple formats    # None
      | format | description        |
      | JSON   | Machine readable   |
      | CSV    | Spreadsheet import |
      | PDF    | Executive summary  |
    And each export should contain all relevant statistics # None
    And the data should be properly formatted              # None

  @monte_carlo_comparison
  Scenario: Compare multiple simulation scenarios                      # features/monte_carlo_simulations.feature:148
    Given I am logged in as a security analyst                         # None
    And I have access to Monte Carlo simulation features               # None
    Given I have completed multiple Monte Carlo simulations            # None
    When I request a comparison analysis                               # None
    Then I should see side-by-side statistics                          # None
    And I should see relative differences between scenarios            # None
    And I should be able to identify the best and worst case scenarios # None

  @monte_carlo_historical
  Scenario: Use historical data for parameter estimation     # features/monte_carlo_simulations.feature:156
    Given I am logged in as a security analyst               # None
    And I have access to Monte Carlo simulation features     # None
    Given I have historical breach cost data                 # None
    When I use it to estimate distribution parameters        # None
    Then the system should suggest appropriate distributions # None
    And the parameters should fit the historical data        # None
    And I should see goodness-of-fit statistics              # None

  @monte_carlo_real_time
  Scenario: Monitor simulation progress in real-time           # features/monte_carlo_simulations.feature:164
    Given I am logged in as a security analyst                 # None
    And I have access to Monte Carlo simulation features       # None
    Given I have started a long-running Monte Carlo simulation # None
    When I check the simulation status                         # None
    Then I should see current progress percentage              # None
    And I should see estimated time to completion              # None
    And I should be able to cancel the simulation if needed    # None

  @monte_carlo_batch
  Scenario: Run multiple simulations in batch            # features/monte_carlo_simulations.feature:172
    Given I am logged in as a security analyst           # None
    And I have access to Monte Carlo simulation features # None
    Given I have multiple simulation configurations      # None
    When I submit them as a batch job                    # None
    Then all simulations should run sequentially         # None
    And I should receive notifications as each completes # None
    And I should get a summary report of all results     # None

Feature: ROI Calculations for Security Investments # features/roi_calculations.feature:1
  As a security analyst
  I want to calculate ROI for security investments
  So that I can justify security spending and make informed decisions
  Background:   # features/roi_calculations.feature:6

  @roi_basic
  Scenario: Create basic ROI calculation                    # features/roi_calculations.feature:11
    Given I am logged in as a security analyst              # None
    And I have access to ROI calculation features           # None
    Given I want to calculate ROI for a security investment # None
    When I provide the following investment details         # None
      | field                     | value     |
      | investment_name           | SIEM Tool |
      | initial_investment        | 500000    |
      | annual_operating_cost     | 100000    |
      | implementation_time       | 6         |
      | expected_lifespan         | 5         |
      | risk_reduction_percentage | 25        |
    Then the ROI calculation should be created successfully # None
    And I should receive the calculated ROI percentage      # None
    And I should see the payback period                     # None
    And I should see the net present value                  # None

  @roi_benefits
  Scenario: Calculate security investment benefits       # features/roi_calculations.feature:27
    Given I am logged in as a security analyst           # None
    And I have access to ROI calculation features        # None
    Given I have a ROI calculation for a security tool   # None
    When the system calculates the benefits              # None
    Then I should see quantified risk reduction benefits # None
    And I should see operational efficiency gains        # None
    And I should see compliance cost savings             # None
    And I should see incident response cost reductions   # None

  @roi_costs
  Scenario: Calculate total cost of ownership       # features/roi_calculations.feature:36
    Given I am logged in as a security analyst      # None
    And I have access to ROI calculation features   # None
    Given I have a security investment proposal     # None
    When I calculate the total costs                # None
    Then I should see initial purchase costs        # None
    And I should see implementation costs           # None
    And I should see annual licensing costs         # None
    And I should see training and maintenance costs # None
    And I should see opportunity costs              # None

  @roi_timeframes
  Scenario Outline: Calculate ROI over different timeframes -- @1.1   # features/roi_calculations.feature:55
    Given I am logged in as a security analyst                        # None
    And I have access to ROI calculation features                     # None
    Given I have a security investment with 3 year lifespan           # None
    When I calculate the ROI                                          # None
    Then the calculation should account for the full 3 year period    # None
    And I should see year-by-year breakdown                           # None
    And I should see cumulative ROI progression                       # None

  @roi_timeframes
  Scenario Outline: Calculate ROI over different timeframes -- @1.2   # features/roi_calculations.feature:56
    Given I am logged in as a security analyst                        # None
    And I have access to ROI calculation features                     # None
    Given I have a security investment with 5 year lifespan           # None
    When I calculate the ROI                                          # None
    Then the calculation should account for the full 5 year period    # None
    And I should see year-by-year breakdown                           # None
    And I should see cumulative ROI progression                       # None

  @roi_timeframes
  Scenario Outline: Calculate ROI over different timeframes -- @1.3   # features/roi_calculations.feature:57
    Given I am logged in as a security analyst                        # None
    And I have access to ROI calculation features                     # None
    Given I have a security investment with 7 year lifespan           # None
    When I calculate the ROI                                          # None
    Then the calculation should account for the full 7 year period    # None
    And I should see year-by-year breakdown                           # None
    And I should see cumulative ROI progression                       # None

  @roi_timeframes
  Scenario Outline: Calculate ROI over different timeframes -- @1.4   # features/roi_calculations.feature:58
    Given I am logged in as a security analyst                        # None
    And I have access to ROI calculation features                     # None
    Given I have a security investment with 10 year lifespan          # None
    When I calculate the ROI                                          # None
    Then the calculation should account for the full 10 year period   # None
    And I should see year-by-year breakdown                           # None
    And I should see cumulative ROI progression                       # None

  @roi_discount_rate
  Scenario: Apply discount rate for NPV calculation           # features/roi_calculations.feature:61
    Given I am logged in as a security analyst                # None
    And I have access to ROI calculation features             # None
    Given I have a ROI calculation                            # None
    When I apply a discount rate of 8%                        # None
    Then the NPV should be calculated using the discount rate # None
    And future cash flows should be properly discounted       # None
    And I should see the impact of the discount rate on ROI   # None

  @roi_sensitivity
  Scenario: Perform ROI sensitivity analysis                    # features/roi_calculations.feature:69
    Given I am logged in as a security analyst                  # None
    And I have access to ROI calculation features               # None
    Given I have a completed ROI calculation                    # None
    When I perform sensitivity analysis on key parameters       # None
    Then I should see how ROI changes with parameter variations # None
    And I should identify the most sensitive parameters         # None
    And I should see break-even analysis                        # None

  @roi_comparison
  Scenario: Compare multiple security investments             # features/roi_calculations.feature:77
    Given I am logged in as a security analyst                # None
    And I have access to ROI calculation features             # None
    Given I have ROI calculations for multiple security tools # None
    When I request a comparison analysis                      # None
    Then I should see side-by-side ROI comparisons            # None
    And I should see ranking by ROI percentage                # None
    And I should see ranking by payback period                # None
    And I should see risk-adjusted comparisons                # None

  @roi_industry_benchmarks
  Scenario: Compare against industry benchmarks                # features/roi_calculations.feature:86
    Given I am logged in as a security analyst                 # None
    And I have access to ROI calculation features              # None
    Given I have a ROI calculation for my organization         # None
    When I request industry benchmark comparison               # None
    Then I should see how my ROI compares to industry averages # None
    And I should see percentile rankings                       # None
    And I should see recommendations for improvement           # None

  @roi_risk_adjustment
  Scenario: Apply risk adjustments to ROI                             # features/roi_calculations.feature:94
    Given I am logged in as a security analyst                        # None
    And I have access to ROI calculation features                     # None
    Given I have a basic ROI calculation                              # None
    When I apply risk adjustments based on implementation uncertainty # None
    Then I should see risk-adjusted ROI values                        # None
    And I should see confidence intervals around the ROI              # None
    And I should see probability of achieving target ROI              # None

  @roi_compliance
  Scenario: Include compliance benefits in ROI                    # features/roi_calculations.feature:102
    Given I am logged in as a security analyst                    # None
    And I have access to ROI calculation features                 # None
    Given I have a security investment that helps with compliance # None
    When I calculate ROI including compliance benefits            # None
    Then I should see avoided compliance penalties                # None
    And I should see reduced audit costs                          # None
    And I should see faster compliance certification benefits     # None

  @roi_incident_prevention
  Scenario: Calculate incident prevention benefits           # features/roi_calculations.feature:110
    Given I am logged in as a security analyst               # None
    And I have access to ROI calculation features            # None
    Given I have historical incident data                    # None
    When I calculate ROI for a preventive security measure   # None
    Then I should see estimated incident prevention benefits # None
    And I should see reduced incident response costs         # None
    And I should see business continuity improvements        # None

  @roi_productivity
  Scenario: Include productivity gains in ROI                       # features/roi_calculations.feature:118
    Given I am logged in as a security analyst                      # None
    And I have access to ROI calculation features                   # None
    Given I have a security tool that improves analyst productivity # None
    When I calculate the ROI                                        # None
    Then I should see time savings quantified                       # None
    And I should see reduced manual effort costs                    # None
    And I should see improved response time benefits                # None

  @roi_validation
  Scenario: Validate ROI calculation inputs                         # features/roi_calculations.feature:126
    Given I am logged in as a security analyst                      # None
    And I have access to ROI calculation features                   # None
    Given I want to create a ROI calculation                        # None
    When I provide invalid input values                             # None
    Then the system should validate the inputs                      # None
    And I should receive specific error messages for invalid fields # None
    And I should be guided to provide correct values                # None

  @roi_templates
  Scenario: Use ROI calculation templates                         # features/roi_calculations.feature:134
    Given I am logged in as a security analyst                    # None
    And I have access to ROI calculation features                 # None
    Given I want to calculate ROI for a common security tool type # None
    When I select a pre-built template                            # None
    Then the template should pre-populate relevant fields         # None
    And I should be able to customize the template                # None
    And I should see industry-standard assumptions                # None

  @roi_export
  Scenario: Export ROI analysis for presentation                # features/roi_calculations.feature:142
    Given I am logged in as a security analyst                  # None
    And I have access to ROI calculation features               # None
    Given I have completed a ROI calculation                    # None
    When I export the analysis                                  # None
    Then I should be able to export in executive summary format # None
    And I should be able to export detailed technical analysis  # None
    And I should be able to export charts and graphs            # None

  @roi_tracking
  Scenario: Track actual vs projected ROI                     # features/roi_calculations.feature:150
    Given I am logged in as a security analyst                # None
    And I have access to ROI calculation features             # None
    Given I have implemented a security investment            # None
    When I track the actual performance against projections   # None
    Then I should be able to update actual costs and benefits # None
    And I should see variance analysis                        # None
    And I should see lessons learned for future calculations  # None

  @roi_approval_workflow
  Scenario: Submit ROI calculation for approval   # features/roi_calculations.feature:158
    Given I am logged in as a security analyst    # None
    And I have access to ROI calculation features # None
    Given I have completed a ROI calculation      # None
    When I submit it for management approval      # None
    Then it should enter the approval workflow    # None
    And stakeholders should be notified           # None
    And I should be able to track approval status # None

  @roi_collaboration
  Scenario: Collaborate on ROI calculations          # features/roi_calculations.feature:166
    Given I am logged in as a security analyst       # None
    And I have access to ROI calculation features    # None
    Given I have a ROI calculation in progress       # None
    When I share it with team members                # None
    Then they should be able to view and comment     # None
    And they should be able to suggest modifications # None
    And I should see all collaboration history       # None

  @roi_version_control
  Scenario: Maintain ROI calculation versions           # features/roi_calculations.feature:174
    Given I am logged in as a security analyst          # None
    And I have access to ROI calculation features       # None
    Given I have a ROI calculation                      # None
    When I make modifications to the calculation        # None
    Then the system should maintain version history     # None
    And I should be able to compare versions            # None
    And I should be able to revert to previous versions # None

  @roi_integration
  Scenario: Integrate ROI with enhanced risk calculations       # features/roi_calculations.feature:182
    Given I am logged in as a security analyst                  # None
    And I have access to ROI calculation features               # None
    Given I have a basic ROI calculation                        # None
    When I enhance it with Monte Carlo risk analysis            # None
    Then the enhanced calculation should reference the base ROI # None
    And I should see risk-adjusted ROI values                   # None
    And I should maintain traceability between calculations     # None

Feature: User Management and Authentication # features/user_management.feature:1
  As a system administrator
  I want to manage user accounts and authentication
  So that I can control access to the CSO platform
  Background:   # features/user_management.feature:6

  @user_registration
  Scenario: Register a new security analyst           # features/user_management.feature:11
    Given the CSO platform is running                 # None
    And I have administrative privileges              # None
    Given I want to register a new user               # None
    When I provide the following registration details # None
      | field                   | value                   |
      | email                   | <EMAIL>     |
      | username                | security_analyst        |
      | full_name               | John Security           |
      | password                | SecurePass123!          |
      | confirm_password        | SecurePass123!          |
      | role                    | security_analyst        |
      | organization            | TechCorp Inc            |
      | department              | IT Security             |
      | job_title               | Senior Security Analyst |
      | terms_accepted          | true                    |
      | privacy_policy_accepted | true                    |
    Then the user should be registered successfully   # None
    And the user should receive a confirmation email  # None
    And the password should be securely hashed        # None

  @user_registration
  Scenario: Reject registration with mismatched passwords                                                       # features/user_management.feature:31
    Given the CSO platform is running                                                                           # None
    And I have administrative privileges                                                                        # None
    Given I want to register a new user                                                                         # None
    When I provide registration details with password "SecurePass123!" and confirm_password "DifferentPass456!" # None
    Then the registration should be rejected                                                                    # None
    And I should receive an error about password mismatch                                                       # None

  @user_authentication
  Scenario: Successful user login                                                             # features/user_management.feature:38
    Given the CSO platform is running                                                         # None
    And I have administrative privileges                                                      # None
    Given I have a registered user with username "test_analyst" and password "SecurePass123!" # None
    When I attempt to login with username "test_analyst" and password "SecurePass123!"        # None
    Then the login should be successful                                                       # None
    And I should receive a valid JWT token                                                    # None
    And the token should contain the user's role and permissions                              # None

  @user_authentication
  Scenario: Failed login with wrong password                                                  # features/user_management.feature:46
    Given the CSO platform is running                                                         # None
    And I have administrative privileges                                                      # None
    Given I have a registered user with username "test_analyst" and password "SecurePass123!" # None
    When I attempt to login with username "test_analyst" and password "WrongPassword"         # None
    Then the login should fail                                                                # None
    And I should receive an authentication error                                              # None
    And the failed login attempt should be recorded                                           # None

  @user_authentication
  Scenario: Account lockout after multiple failed attempts                     # features/user_management.feature:54
    Given the CSO platform is running                                          # None
    And I have administrative privileges                                       # None
    Given I have a registered user with username "test_analyst"                # None
    When I attempt to login with wrong password 5 times                        # None
    Then the account should be locked                                          # None
    And subsequent login attempts should be rejected with account locked error # None
    And the user should receive an account lockout notification                # None

  @user_profile
  Scenario: Update user profile information                 # features/user_management.feature:62
    Given the CSO platform is running                       # None
    And I have administrative privileges                    # None
    Given I am logged in as "test_analyst"                  # None
    When I update my profile with the following information # None
      | field        | value                 |
      | full_name    | John Updated Name     |
      | organization | Updated Corp          |
      | department   | Cybersecurity         |
      | job_title    | Lead Security Analyst |
      | phone        | ******-0123           |
      | timezone     | America/New_York      |
    Then my profile should be updated successfully          # None
    And the changes should be reflected in my profile       # None

  @password_management
  Scenario: Change password successfully                                   # features/user_management.feature:76
    Given the CSO platform is running                                      # None
    And I have administrative privileges                                   # None
    Given I am logged in as "test_analyst"                                 # None
    When I change my password from "SecurePass123!" to "NewSecurePass456!" # None
    Then the password change should be successful                          # None
    And I should be able to login with the new password                    # None
    And the old password should no longer work                             # None

  @password_management
  Scenario: Reject password change with wrong current password       # features/user_management.feature:84
    Given the CSO platform is running                                # None
    And I have administrative privileges                             # None
    Given I am logged in as "test_analyst"                           # None
    When I attempt to change my password with wrong current password # None
    Then the password change should be rejected                      # None
    And I should receive an error about incorrect current password   # None

  @user_roles
  Scenario: Security analyst can access risk calculations     # features/user_management.feature:91
    Given the CSO platform is running                         # None
    And I have administrative privileges                      # None
    Given I am logged in as a "security_analyst"              # None
    When I try to access the risk calculation features        # None
    Then I should have access to create and view calculations # None
    And I should be able to create risk profiles              # None

  @user_roles
  Scenario: CISO can access all platform features        # features/user_management.feature:98
    Given the CSO platform is running                    # None
    And I have administrative privileges                 # None
    Given I am logged in as a "ciso"                     # None
    When I try to access administrative features         # None
    Then I should have access to all platform features   # None
    And I should be able to view all users' calculations # None
    And I should be able to manage user accounts         # None

  @user_roles
  Scenario: Auditor has read-only access                      # features/user_management.feature:106
    Given the CSO platform is running                         # None
    And I have administrative privileges                      # None
    Given I am logged in as an "auditor"                      # None
    When I try to access calculation features                 # None
    Then I should have read-only access to calculations       # None
    And I should not be able to create or modify calculations # None
    And I should be able to generate audit reports            # None

  @user_deactivation
  Scenario: Deactivate user account                    # features/user_management.feature:114
    Given the CSO platform is running                  # None
    And I have administrative privileges               # None
    Given I have a registered user "inactive_user"     # None
    And I am logged in as an administrator             # None
    When I deactivate the user account "inactive_user" # None
    Then the account should be deactivated             # None
    And the user should not be able to login           # None
    And existing sessions should be invalidated        # None

  @data_privacy
  Scenario: User can only access their own data       # features/user_management.feature:123
    Given the CSO platform is running                 # None
    And I have administrative privileges              # None
    Given I am logged in as "user1"                   # None
    And another user "user2" has created calculations # None
    When I try to access "user2"'s calculations       # None
    Then I should receive an access denied error      # None
    And I should only see my own calculations         # None

  @session_management
  Scenario: JWT token expires after configured time     # features/user_management.feature:131
    Given the CSO platform is running                   # None
    And I have administrative privileges                # None
    Given I am logged in with a JWT token               # None
    When the token expires after the configured timeout # None
    Then subsequent API requests should be rejected     # None
    And I should receive an authentication error        # None
    And I should need to login again                    # None

  @session_management
  Scenario: Refresh JWT token before expiration                 # features/user_management.feature:139
    Given the CSO platform is running                           # None
    And I have administrative privileges                        # None
    Given I am logged in with a JWT token that will expire soon # None
    When I request a token refresh                              # None
    Then I should receive a new valid token                     # None
    And the new token should have extended expiration time      # None
    And I should be able to continue using the platform         # None

  @user_validation
  Scenario: Reject invalid email format                        # features/user_management.feature:147
    Given the CSO platform is running                          # None
    And I have administrative privileges                       # None
    Given I want to register a new user                        # None
    When I provide an invalid email format "not-an-email"      # None
    Then the registration should be rejected                   # None
    And I should receive a validation error about email format # None

  @user_validation
  Scenario: Reject weak password                                    # features/user_management.feature:154
    Given the CSO platform is running                               # None
    And I have administrative privileges                            # None
    Given I want to register a new user                             # None
    When I provide a weak password "123"                            # None
    Then the registration should be rejected                        # None
    And I should receive a validation error about password strength # None

  @user_validation
  Scenario: Reject duplicate username                                 # features/user_management.feature:161
    Given the CSO platform is running                                 # None
    And I have administrative privileges                              # None
    Given I have a registered user with username "existing_user"      # None
    When I try to register another user with username "existing_user" # None
    Then the registration should be rejected                          # None
    And I should receive an error about username already exists       # None

  @user_validation
  Scenario: Reject duplicate email                                        # features/user_management.feature:168
    Given the CSO platform is running                                     # None
    And I have administrative privileges                                  # None
    Given I have a registered user with email "<EMAIL>"      # None
    When I try to register another user with email "<EMAIL>" # None
    Then the registration should be rejected                              # None
    And I should receive an error about email already exists              # None

