Feature: Enhanced Risk Modeling and Monte Carlo Simulations # features/enhanced_risk_modeling.feature:1
  As a security analyst
  I want to perform advanced risk calculations with Monte Carlo simulations
  So that I can make data-driven investment decisions with confidence intervals
  Background:   # features/enhanced_risk_modeling.feature:6

  @risk_profile
  Scenario: Create a healthcare organization risk profile                                      # features/enhanced_risk_modeling.feature:11
    Given I am a logged-in security analyst                                                    # features/steps/enhanced_risk_steps.py:29
    And I have access to the enhanced risk modeling features                                   # features/steps/enhanced_risk_steps.py:36
    Given I want to create a risk profile for my organization                                  # features/steps/enhanced_risk_steps.py:45
    When I provide the following organization details                                          # features/steps/enhanced_risk_steps.py:51
      | field                     | value                    |
      | name                      | Regional Healthcare Corp |
      | industry                  | healthcare               |
      | organization_size         | medium                   |
      | annual_revenue            | 50000000                 |
      | employee_count            | 1200                     |
      | data_sensitivity_level    | 5                        |
      | regulatory_requirements   | HIPAA,SOC2               |
      | previous_incidents        | 2                        |
      | current_security_maturity | 3                        |
      | business_criticality      | high                     |
    Then the risk profile should be created successfully                                       # features/steps/enhanced_risk_steps.py:68
      Traceback (most recent call last):
        File "/nix/store/br8fp0vbrx06mbk02bdhm571a3c0gjdg-python3-3.11.11-env/lib/python3.11/site-packages/behave/model.py", line 1812, in run
          match.run(runner.context)
        File "/nix/store/br8fp0vbrx06mbk02bdhm571a3c0gjdg-python3-3.11.11-env/lib/python3.11/site-packages/behave/matchers.py", line 103, in run
          self.func(context, *args, **kwargs)
        File "features/steps/enhanced_risk_steps.py", line 74, in step_risk_profile_created_successfully
          response = requests.post(
                     ^^^^^^^^^^^^^^
        File "/nix/store/br8fp0vbrx06mbk02bdhm571a3c0gjdg-python3-3.11.11-env/lib/python3.11/site-packages/requests/api.py", line 115, in post
          return request("post", url, data=data, json=json, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        File "/nix/store/br8fp0vbrx06mbk02bdhm571a3c0gjdg-python3-3.11.11-env/lib/python3.11/site-packages/requests/api.py", line 59, in request
          return session.request(method=method, url=url, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        File "/nix/store/br8fp0vbrx06mbk02bdhm571a3c0gjdg-python3-3.11.11-env/lib/python3.11/site-packages/requests/sessions.py", line 575, in request
          prep = self.prepare_request(req)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^
        File "/nix/store/br8fp0vbrx06mbk02bdhm571a3c0gjdg-python3-3.11.11-env/lib/python3.11/site-packages/requests/sessions.py", line 484, in prepare_request
          p.prepare(
        File "/nix/store/br8fp0vbrx06mbk02bdhm571a3c0gjdg-python3-3.11.11-env/lib/python3.11/site-packages/requests/models.py", line 370, in prepare
          self.prepare_body(data, files, json)
        File "/nix/store/br8fp0vbrx06mbk02bdhm571a3c0gjdg-python3-3.11.11-env/lib/python3.11/site-packages/requests/models.py", line 510, in prepare_body
          body = complexjson.dumps(json, allow_nan=False)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        File "/nix/store/r3jc6l5fahs214qdiin5bh8cjrcgyv2f-python3-3.11.11/lib/python3.11/json/__init__.py", line 238, in dumps
          **kw).encode(obj)
                ^^^^^^^^^^^
        File "/nix/store/r3jc6l5fahs214qdiin5bh8cjrcgyv2f-python3-3.11.11/lib/python3.11/json/encoder.py", line 200, in encode
          chunks = self.iterencode(o, _one_shot=True)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        File "/nix/store/r3jc6l5fahs214qdiin5bh8cjrcgyv2f-python3-3.11.11/lib/python3.11/json/encoder.py", line 258, in iterencode
          return _iterencode(o, 0)
                 ^^^^^^^^^^^^^^^^^
        File "/nix/store/r3jc6l5fahs214qdiin5bh8cjrcgyv2f-python3-3.11.11/lib/python3.11/json/encoder.py", line 180, in default
          raise TypeError(f'Object of type {o.__class__.__name__} '
      TypeError: Object of type Decimal is not JSON serializable

    And the breach cost multiplier should be calculated based on healthcare industry standards # None
    And the breach probability should reflect the organization's security maturity             # None

