<testsuite name="user_management.User Management and Authentication" tests="19" errors="0" failures="0" skipped="19" time="0.0" timestamp="2025-06-20T17:13:43.072134" hostname="ganymede"><testcase classname="user_management.User Management and Authentication" name="Register a new security analyst" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @user_registration
  Scenario: Register a new security analyst
    Given the CSO platform is running ... skipped in 0.000s
    And I have administrative privileges ... skipped in 0.000s
    Given I want to register a new user ... skipped in 0.000s
    When I provide the following registration details ... skipped in 0.000s
      | field                   | value                   |
      | email                   | <EMAIL>     |
      | username                | security_analyst        |
      | full_name               | John Security           |
      | password                | SecurePass123!          |
      | confirm_password        | SecurePass123!          |
      | role                    | security_analyst        |
      | organization            | TechCorp Inc            |
      | department              | IT Security             |
      | job_title               | Senior Security Analyst |
      | terms_accepted          | true                    |
      | privacy_policy_accepted | true                    |
    Then the user should be registered successfully ... skipped in 0.000s
    And the user should receive a confirmation email ... skipped in 0.000s
    And the password should be securely hashed ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Reject registration with mismatched passwords" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @user_registration
  Scenario: Reject registration with mismatched passwords
    Given the CSO platform is running ... skipped in 0.000s
    And I have administrative privileges ... skipped in 0.000s
    Given I want to register a new user ... skipped in 0.000s
    When I provide registration details with password "SecurePass123!" and confirm_password "DifferentPass456!" ... skipped in 0.000s
    Then the registration should be rejected ... skipped in 0.000s
    And I should receive an error about password mismatch ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Successful user login" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @user_authentication
  Scenario: Successful user login
    Given the CSO platform is running ... skipped in 0.000s
    And I have administrative privileges ... skipped in 0.000s
    Given I have a registered user with username "test_analyst" and password "SecurePass123!" ... skipped in 0.000s
    When I attempt to login with username "test_analyst" and password "SecurePass123!" ... skipped in 0.000s
    Then the login should be successful ... skipped in 0.000s
    And I should receive a valid JWT token ... skipped in 0.000s
    And the token should contain the user's role and permissions ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Failed login with wrong password" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @user_authentication
  Scenario: Failed login with wrong password
    Given the CSO platform is running ... skipped in 0.000s
    And I have administrative privileges ... skipped in 0.000s
    Given I have a registered user with username "test_analyst" and password "SecurePass123!" ... skipped in 0.000s
    When I attempt to login with username "test_analyst" and password "WrongPassword" ... skipped in 0.000s
    Then the login should fail ... skipped in 0.000s
    And I should receive an authentication error ... skipped in 0.000s
    And the failed login attempt should be recorded ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Account lockout after multiple failed attempts" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @user_authentication
  Scenario: Account lockout after multiple failed attempts
    Given the CSO platform is running ... skipped in 0.000s
    And I have administrative privileges ... skipped in 0.000s
    Given I have a registered user with username "test_analyst" ... skipped in 0.000s
    When I attempt to login with wrong password 5 times ... skipped in 0.000s
    Then the account should be locked ... skipped in 0.000s
    And subsequent login attempts should be rejected with account locked error ... skipped in 0.000s
    And the user should receive an account lockout notification ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Update user profile information" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @user_profile
  Scenario: Update user profile information
    Given the CSO platform is running ... skipped in 0.000s
    And I have administrative privileges ... skipped in 0.000s
    Given I am logged in as "test_analyst" ... skipped in 0.000s
    When I update my profile with the following information ... skipped in 0.000s
      | field        | value                 |
      | full_name    | John Updated Name     |
      | organization | Updated Corp          |
      | department   | Cybersecurity         |
      | job_title    | Lead Security Analyst |
      | phone        | ******-0123           |
      | timezone     | America/New_York      |
    Then my profile should be updated successfully ... skipped in 0.000s
    And the changes should be reflected in my profile ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Change password successfully" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @password_management
  Scenario: Change password successfully
    Given the CSO platform is running ... skipped in 0.000s
    And I have administrative privileges ... skipped in 0.000s
    Given I am logged in as "test_analyst" ... skipped in 0.000s
    When I change my password from "SecurePass123!" to "NewSecurePass456!" ... skipped in 0.000s
    Then the password change should be successful ... skipped in 0.000s
    And I should be able to login with the new password ... skipped in 0.000s
    And the old password should no longer work ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Reject password change with wrong current password" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @password_management
  Scenario: Reject password change with wrong current password
    Given the CSO platform is running ... skipped in 0.000s
    And I have administrative privileges ... skipped in 0.000s
    Given I am logged in as "test_analyst" ... skipped in 0.000s
    When I attempt to change my password with wrong current password ... skipped in 0.000s
    Then the password change should be rejected ... skipped in 0.000s
    And I should receive an error about incorrect current password ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Security analyst can access risk calculations" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @user_roles
  Scenario: Security analyst can access risk calculations
    Given the CSO platform is running ... skipped in 0.000s
    And I have administrative privileges ... skipped in 0.000s
    Given I am logged in as a "security_analyst" ... skipped in 0.000s
    When I try to access the risk calculation features ... skipped in 0.000s
    Then I should have access to create and view calculations ... skipped in 0.000s
    And I should be able to create risk profiles ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="CISO can access all platform features" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @user_roles
  Scenario: CISO can access all platform features
    Given the CSO platform is running ... skipped in 0.000s
    And I have administrative privileges ... skipped in 0.000s
    Given I am logged in as a "ciso" ... skipped in 0.000s
    When I try to access administrative features ... skipped in 0.000s
    Then I should have access to all platform features ... skipped in 0.000s
    And I should be able to view all users' calculations ... skipped in 0.000s
    And I should be able to manage user accounts ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Auditor has read-only access" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @user_roles
  Scenario: Auditor has read-only access
    Given the CSO platform is running ... skipped in 0.000s
    And I have administrative privileges ... skipped in 0.000s
    Given I am logged in as an "auditor" ... skipped in 0.000s
    When I try to access calculation features ... skipped in 0.000s
    Then I should have read-only access to calculations ... skipped in 0.000s
    And I should not be able to create or modify calculations ... skipped in 0.000s
    And I should be able to generate audit reports ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Deactivate user account" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @user_deactivation
  Scenario: Deactivate user account
    Given the CSO platform is running ... skipped in 0.000s
    And I have administrative privileges ... skipped in 0.000s
    Given I have a registered user "inactive_user" ... skipped in 0.000s
    And I am logged in as an administrator ... skipped in 0.000s
    When I deactivate the user account "inactive_user" ... skipped in 0.000s
    Then the account should be deactivated ... skipped in 0.000s
    And the user should not be able to login ... skipped in 0.000s
    And existing sessions should be invalidated ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="User can only access their own data" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @data_privacy
  Scenario: User can only access their own data
    Given the CSO platform is running ... skipped in 0.000s
    And I have administrative privileges ... skipped in 0.000s
    Given I am logged in as "user1" ... skipped in 0.000s
    And another user "user2" has created calculations ... skipped in 0.000s
    When I try to access "user2"'s calculations ... skipped in 0.000s
    Then I should receive an access denied error ... skipped in 0.000s
    And I should only see my own calculations ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="JWT token expires after configured time" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @session_management
  Scenario: JWT token expires after configured time
    Given the CSO platform is running ... skipped in 0.000s
    And I have administrative privileges ... skipped in 0.000s
    Given I am logged in with a JWT token ... skipped in 0.000s
    When the token expires after the configured timeout ... skipped in 0.000s
    Then subsequent API requests should be rejected ... skipped in 0.000s
    And I should receive an authentication error ... skipped in 0.000s
    And I should need to login again ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Refresh JWT token before expiration" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @session_management
  Scenario: Refresh JWT token before expiration
    Given the CSO platform is running ... skipped in 0.000s
    And I have administrative privileges ... skipped in 0.000s
    Given I am logged in with a JWT token that will expire soon ... skipped in 0.000s
    When I request a token refresh ... skipped in 0.000s
    Then I should receive a new valid token ... skipped in 0.000s
    And the new token should have extended expiration time ... skipped in 0.000s
    And I should be able to continue using the platform ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Reject invalid email format" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @user_validation
  Scenario: Reject invalid email format
    Given the CSO platform is running ... skipped in 0.000s
    And I have administrative privileges ... skipped in 0.000s
    Given I want to register a new user ... skipped in 0.000s
    When I provide an invalid email format "not-an-email" ... skipped in 0.000s
    Then the registration should be rejected ... skipped in 0.000s
    And I should receive a validation error about email format ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Reject weak password" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @user_validation
  Scenario: Reject weak password
    Given the CSO platform is running ... skipped in 0.000s
    And I have administrative privileges ... skipped in 0.000s
    Given I want to register a new user ... skipped in 0.000s
    When I provide a weak password "123" ... skipped in 0.000s
    Then the registration should be rejected ... skipped in 0.000s
    And I should receive a validation error about password strength ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Reject duplicate username" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @user_validation
  Scenario: Reject duplicate username
    Given the CSO platform is running ... skipped in 0.000s
    And I have administrative privileges ... skipped in 0.000s
    Given I have a registered user with username "existing_user" ... skipped in 0.000s
    When I try to register another user with username "existing_user" ... skipped in 0.000s
    Then the registration should be rejected ... skipped in 0.000s
    And I should receive an error about username already exists ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Reject duplicate email" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @user_validation
  Scenario: Reject duplicate email
    Given the CSO platform is running ... skipped in 0.000s
    And I have administrative privileges ... skipped in 0.000s
    Given I have a registered user with email "<EMAIL>" ... skipped in 0.000s
    When I try to register another user with email "<EMAIL>" ... skipped in 0.000s
    Then the registration should be rejected ... skipped in 0.000s
    And I should receive an error about email already exists ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase></testsuite>