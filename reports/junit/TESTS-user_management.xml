<testsuite name="user_management.User Management and Authentication" tests="19" errors="0" failures="18" skipped="19" time="0.0" timestamp="2025-06-20T16:47:32.052600" hostname="ganymede"><testcase classname="user_management.User Management and Authentication" name="Register a new security analyst" status="untested" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @user_registration
  Scenario: Register a new security analyst
    Given the CSO platform is running ... untested in 0.000s
    And I have administrative privileges ... untested in 0.000s
    Given I want to register a new user ... untested in 0.000s
    When I provide the following registration details ... untested in 0.000s
      | field                   | value                   |
      | email                   | <EMAIL>     |
      | username                | security_analyst        |
      | full_name               | John Security           |
      | password                | SecurePass123!          |
      | confirm_password        | SecurePass123!          |
      | role                    | security_analyst        |
      | organization            | TechCorp Inc            |
      | department              | IT Security             |
      | job_title               | Senior Security Analyst |
      | terms_accepted          | true                    |
      | privacy_policy_accepted | true                    |
    Then the user should be registered successfully ... untested in 0.000s
    And the user should receive a confirmation email ... untested in 0.000s
    And the password should be securely hashed ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Reject registration with mismatched passwords" status="untested" time="0"><failure type="undefined" message="Undefined Step: I provide registration details with password &quot;SecurePass123!&quot; and confirm_password &quot;DifferentPass456!&quot;" /><system-out>
<![CDATA[
@scenario.begin

  @user_registration
  Scenario: Reject registration with mismatched passwords
    Given the CSO platform is running ... untested in 0.000s
    And I have administrative privileges ... untested in 0.000s
    Given I want to register a new user ... untested in 0.000s
    When I provide registration details with password "SecurePass123!" and confirm_password "DifferentPass456!" ... undefined in 0.000s
    Then the registration should be rejected ... untested in 0.000s
    And I should receive an error about password mismatch ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Successful user login" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a registered user with username &quot;test_analyst&quot; and password &quot;SecurePass123!&quot;" /><system-out>
<![CDATA[
@scenario.begin

  @user_authentication
  Scenario: Successful user login
    Given the CSO platform is running ... untested in 0.000s
    And I have administrative privileges ... untested in 0.000s
    Given I have a registered user with username "test_analyst" and password "SecurePass123!" ... undefined in 0.000s
    When I attempt to login with username "test_analyst" and password "SecurePass123!" ... undefined in 0.000s
    Then the login should be successful ... untested in 0.000s
    And I should receive a valid JWT token ... untested in 0.000s
    And the token should contain the user's role and permissions ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Failed login with wrong password" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a registered user with username &quot;test_analyst&quot; and password &quot;SecurePass123!&quot;" /><system-out>
<![CDATA[
@scenario.begin

  @user_authentication
  Scenario: Failed login with wrong password
    Given the CSO platform is running ... untested in 0.000s
    And I have administrative privileges ... untested in 0.000s
    Given I have a registered user with username "test_analyst" and password "SecurePass123!" ... undefined in 0.000s
    When I attempt to login with username "test_analyst" and password "WrongPassword" ... undefined in 0.000s
    Then the login should fail ... untested in 0.000s
    And I should receive an authentication error ... untested in 0.000s
    And the failed login attempt should be recorded ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Account lockout after multiple failed attempts" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a registered user with username &quot;test_analyst&quot;" /><system-out>
<![CDATA[
@scenario.begin

  @user_authentication
  Scenario: Account lockout after multiple failed attempts
    Given the CSO platform is running ... untested in 0.000s
    And I have administrative privileges ... untested in 0.000s
    Given I have a registered user with username "test_analyst" ... undefined in 0.000s
    When I attempt to login with wrong password 5 times ... undefined in 0.000s
    Then the account should be locked ... untested in 0.000s
    And subsequent login attempts should be rejected with account locked error ... untested in 0.000s
    And the user should receive an account lockout notification ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Update user profile information" status="untested" time="0"><failure type="undefined" message="Undefined Step: I am logged in as &quot;test_analyst&quot;" /><system-out>
<![CDATA[
@scenario.begin

  @user_profile
  Scenario: Update user profile information
    Given the CSO platform is running ... untested in 0.000s
    And I have administrative privileges ... untested in 0.000s
    Given I am logged in as "test_analyst" ... undefined in 0.000s
    When I update my profile with the following information ... untested in 0.000s
      | field        | value                 |
      | full_name    | John Updated Name     |
      | organization | Updated Corp          |
      | department   | Cybersecurity         |
      | job_title    | Lead Security Analyst |
      | phone        | ******-0123           |
      | timezone     | America/New_York      |
    Then my profile should be updated successfully ... untested in 0.000s
    And the changes should be reflected in my profile ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Change password successfully" status="untested" time="0"><failure type="undefined" message="Undefined Step: I am logged in as &quot;test_analyst&quot;" /><system-out>
<![CDATA[
@scenario.begin

  @password_management
  Scenario: Change password successfully
    Given the CSO platform is running ... untested in 0.000s
    And I have administrative privileges ... untested in 0.000s
    Given I am logged in as "test_analyst" ... undefined in 0.000s
    When I change my password from "SecurePass123!" to "NewSecurePass456!" ... undefined in 0.000s
    Then the password change should be successful ... untested in 0.000s
    And I should be able to login with the new password ... untested in 0.000s
    And the old password should no longer work ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Reject password change with wrong current password" status="untested" time="0"><failure type="undefined" message="Undefined Step: I am logged in as &quot;test_analyst&quot;" /><system-out>
<![CDATA[
@scenario.begin

  @password_management
  Scenario: Reject password change with wrong current password
    Given the CSO platform is running ... untested in 0.000s
    And I have administrative privileges ... untested in 0.000s
    Given I am logged in as "test_analyst" ... undefined in 0.000s
    When I attempt to change my password with wrong current password ... untested in 0.000s
    Then the password change should be rejected ... untested in 0.000s
    And I should receive an error about incorrect current password ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Security analyst can access risk calculations" status="untested" time="0"><failure type="undefined" message="Undefined Step: I am logged in as a &quot;security_analyst&quot;" /><system-out>
<![CDATA[
@scenario.begin

  @user_roles
  Scenario: Security analyst can access risk calculations
    Given the CSO platform is running ... untested in 0.000s
    And I have administrative privileges ... untested in 0.000s
    Given I am logged in as a "security_analyst" ... undefined in 0.000s
    When I try to access the risk calculation features ... untested in 0.000s
    Then I should have access to create and view calculations ... untested in 0.000s
    And I should be able to create risk profiles ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="CISO can access all platform features" status="untested" time="0"><failure type="undefined" message="Undefined Step: I am logged in as a &quot;ciso&quot;" /><system-out>
<![CDATA[
@scenario.begin

  @user_roles
  Scenario: CISO can access all platform features
    Given the CSO platform is running ... untested in 0.000s
    And I have administrative privileges ... untested in 0.000s
    Given I am logged in as a "ciso" ... undefined in 0.000s
    When I try to access administrative features ... untested in 0.000s
    Then I should have access to all platform features ... untested in 0.000s
    And I should be able to view all users' calculations ... untested in 0.000s
    And I should be able to manage user accounts ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Auditor has read-only access" status="untested" time="0"><failure type="undefined" message="Undefined Step: I am logged in as an &quot;auditor&quot;" /><system-out>
<![CDATA[
@scenario.begin

  @user_roles
  Scenario: Auditor has read-only access
    Given the CSO platform is running ... untested in 0.000s
    And I have administrative privileges ... untested in 0.000s
    Given I am logged in as an "auditor" ... undefined in 0.000s
    When I try to access calculation features ... undefined in 0.000s
    Then I should have read-only access to calculations ... undefined in 0.000s
    And I should not be able to create or modify calculations ... undefined in 0.000s
    And I should be able to generate audit reports ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Deactivate user account" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a registered user &quot;inactive_user&quot;" /><system-out>
<![CDATA[
@scenario.begin

  @user_deactivation
  Scenario: Deactivate user account
    Given the CSO platform is running ... untested in 0.000s
    And I have administrative privileges ... untested in 0.000s
    Given I have a registered user "inactive_user" ... undefined in 0.000s
    And I am logged in as an administrator ... undefined in 0.000s
    When I deactivate the user account "inactive_user" ... undefined in 0.000s
    Then the account should be deactivated ... undefined in 0.000s
    And the user should not be able to login ... undefined in 0.000s
    And existing sessions should be invalidated ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="User can only access their own data" status="untested" time="0"><failure type="undefined" message="Undefined Step: I am logged in as &quot;user1&quot;" /><system-out>
<![CDATA[
@scenario.begin

  @data_privacy
  Scenario: User can only access their own data
    Given the CSO platform is running ... untested in 0.000s
    And I have administrative privileges ... untested in 0.000s
    Given I am logged in as "user1" ... undefined in 0.000s
    And another user "user2" has created calculations ... undefined in 0.000s
    When I try to access "user2"'s calculations ... undefined in 0.000s
    Then I should receive an access denied error ... undefined in 0.000s
    And I should only see my own calculations ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="JWT token expires after configured time" status="untested" time="0"><failure type="undefined" message="Undefined Step: I am logged in with a JWT token" /><system-out>
<![CDATA[
@scenario.begin

  @session_management
  Scenario: JWT token expires after configured time
    Given the CSO platform is running ... untested in 0.000s
    And I have administrative privileges ... untested in 0.000s
    Given I am logged in with a JWT token ... undefined in 0.000s
    When the token expires after the configured timeout ... undefined in 0.000s
    Then subsequent API requests should be rejected ... undefined in 0.000s
    And I should receive an authentication error ... untested in 0.000s
    And I should need to login again ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Refresh JWT token before expiration" status="untested" time="0"><failure type="undefined" message="Undefined Step: I am logged in with a JWT token that will expire soon" /><system-out>
<![CDATA[
@scenario.begin

  @session_management
  Scenario: Refresh JWT token before expiration
    Given the CSO platform is running ... untested in 0.000s
    And I have administrative privileges ... untested in 0.000s
    Given I am logged in with a JWT token that will expire soon ... undefined in 0.000s
    When I request a token refresh ... undefined in 0.000s
    Then I should receive a new valid token ... undefined in 0.000s
    And the new token should have extended expiration time ... undefined in 0.000s
    And I should be able to continue using the platform ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Reject invalid email format" status="untested" time="0"><failure type="undefined" message="Undefined Step: I provide an invalid email format &quot;not-an-email&quot;" /><system-out>
<![CDATA[
@scenario.begin

  @user_validation
  Scenario: Reject invalid email format
    Given the CSO platform is running ... untested in 0.000s
    And I have administrative privileges ... untested in 0.000s
    Given I want to register a new user ... untested in 0.000s
    When I provide an invalid email format "not-an-email" ... undefined in 0.000s
    Then the registration should be rejected ... untested in 0.000s
    And I should receive a validation error about email format ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Reject weak password" status="untested" time="0"><failure type="undefined" message="Undefined Step: I provide a weak password &quot;123&quot;" /><system-out>
<![CDATA[
@scenario.begin

  @user_validation
  Scenario: Reject weak password
    Given the CSO platform is running ... untested in 0.000s
    And I have administrative privileges ... untested in 0.000s
    Given I want to register a new user ... untested in 0.000s
    When I provide a weak password "123" ... undefined in 0.000s
    Then the registration should be rejected ... untested in 0.000s
    And I should receive a validation error about password strength ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Reject duplicate username" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a registered user with username &quot;existing_user&quot;" /><system-out>
<![CDATA[
@scenario.begin

  @user_validation
  Scenario: Reject duplicate username
    Given the CSO platform is running ... untested in 0.000s
    And I have administrative privileges ... untested in 0.000s
    Given I have a registered user with username "existing_user" ... undefined in 0.000s
    When I try to register another user with username "existing_user" ... undefined in 0.000s
    Then the registration should be rejected ... untested in 0.000s
    And I should receive an error about username already exists ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="user_management.User Management and Authentication" name="Reject duplicate email" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a registered user with email &quot;<EMAIL>&quot;" /><system-out>
<![CDATA[
@scenario.begin

  @user_validation
  Scenario: Reject duplicate email
    Given the CSO platform is running ... untested in 0.000s
    And I have administrative privileges ... untested in 0.000s
    Given I have a registered user with email "<EMAIL>" ... undefined in 0.000s
    When I try to register another user with email "<EMAIL>" ... undefined in 0.000s
    Then the registration should be rejected ... untested in 0.000s
    And I should receive an error about email already exists ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase></testsuite>