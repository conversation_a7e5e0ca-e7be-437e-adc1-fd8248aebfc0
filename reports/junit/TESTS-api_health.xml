<testsuite name="api_health.API Health and Connectivity" tests="3" errors="0" failures="0" skipped="0" time="0.008018" timestamp="2025-06-20T17:13:43.055486" hostname="ganymede"><testcase classname="api_health.API Health and Connectivity" name="API health check responds successfully" status="passed" time="0.003415"><system-out>
<![CDATA[
@scenario.begin

  @health_check
  Scenario: API health check responds successfully
    Given the CSO platform is running ... passed in 0.002s
    When I check the API health endpoint ... passed in 0.001s
    Then the health check should return status "healthy" ... passed in 0.000s
    And the response should include service information ... passed in 0.000s
    And the response should include version information ... passed in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="api_health.API Health and Connectivity" name="API documentation is accessible" status="passed" time="0.002966"><system-out>
<![CDATA[
@scenario.begin

  @api_docs
  Scenario: API documentation is accessible
    Given the CSO platform is running ... passed in 0.001s
    When I access the API documentation ... passed in 0.002s
    Then the documentation should be available ... passed in 0.000s
    And it should include API version information ... passed in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="api_health.API Health and Connectivity" name="Service URL Manager integration works" status="passed" time="0.001637"><system-out>
<![CDATA[
@scenario.begin

  @service_url_manager
  Scenario: Service URL Manager integration works
    Given the CSO platform is running ... passed in 0.001s
    When I test the Service URL Manager configuration ... passed in 0.000s
    Then it should generate correct URLs for the local environment ... passed in 0.000s
    And it should handle different service types correctly ... passed in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase></testsuite>