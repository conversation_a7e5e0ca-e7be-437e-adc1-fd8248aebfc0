<testsuite name="roi_calculations.ROI Calculations for Security Investments" tests="23" errors="0" failures="0" skipped="23" time="0.0" timestamp="2025-06-20T17:13:43.067462" hostname="ganymede"><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Create basic ROI calculation" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_basic
  Scenario: Create basic ROI calculation
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to ROI calculation features ... skipped in 0.000s
    Given I want to calculate ROI for a security investment ... skipped in 0.000s
    When I provide the following investment details ... skipped in 0.000s
      | field                     | value     |
      | investment_name           | SIEM Tool |
      | initial_investment        | 500000    |
      | annual_operating_cost     | 100000    |
      | implementation_time       | 6         |
      | expected_lifespan         | 5         |
      | risk_reduction_percentage | 25        |
    Then the ROI calculation should be created successfully ... skipped in 0.000s
    And I should receive the calculated ROI percentage ... skipped in 0.000s
    And I should see the payback period ... skipped in 0.000s
    And I should see the net present value ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Calculate security investment benefits" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_benefits
  Scenario: Calculate security investment benefits
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to ROI calculation features ... skipped in 0.000s
    Given I have a ROI calculation for a security tool ... skipped in 0.000s
    When the system calculates the benefits ... skipped in 0.000s
    Then I should see quantified risk reduction benefits ... skipped in 0.000s
    And I should see operational efficiency gains ... skipped in 0.000s
    And I should see compliance cost savings ... skipped in 0.000s
    And I should see incident response cost reductions ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Calculate total cost of ownership" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_costs
  Scenario: Calculate total cost of ownership
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to ROI calculation features ... skipped in 0.000s
    Given I have a security investment proposal ... skipped in 0.000s
    When I calculate the total costs ... skipped in 0.000s
    Then I should see initial purchase costs ... skipped in 0.000s
    And I should see implementation costs ... skipped in 0.000s
    And I should see annual licensing costs ... skipped in 0.000s
    And I should see training and maintenance costs ... skipped in 0.000s
    And I should see opportunity costs ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Calculate ROI over different timeframes -- @1.1 " status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_timeframes
  Scenario Outline: Calculate ROI over different timeframes -- @1.1 
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to ROI calculation features ... skipped in 0.000s
    Given I have a security investment with 3 year lifespan ... skipped in 0.000s
    When I calculate the ROI ... skipped in 0.000s
    Then the calculation should account for the full 3 year period ... skipped in 0.000s
    And I should see year-by-year breakdown ... skipped in 0.000s
    And I should see cumulative ROI progression ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Calculate ROI over different timeframes -- @1.2 " status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_timeframes
  Scenario Outline: Calculate ROI over different timeframes -- @1.2 
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to ROI calculation features ... skipped in 0.000s
    Given I have a security investment with 5 year lifespan ... skipped in 0.000s
    When I calculate the ROI ... skipped in 0.000s
    Then the calculation should account for the full 5 year period ... skipped in 0.000s
    And I should see year-by-year breakdown ... skipped in 0.000s
    And I should see cumulative ROI progression ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Calculate ROI over different timeframes -- @1.3 " status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_timeframes
  Scenario Outline: Calculate ROI over different timeframes -- @1.3 
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to ROI calculation features ... skipped in 0.000s
    Given I have a security investment with 7 year lifespan ... skipped in 0.000s
    When I calculate the ROI ... skipped in 0.000s
    Then the calculation should account for the full 7 year period ... skipped in 0.000s
    And I should see year-by-year breakdown ... skipped in 0.000s
    And I should see cumulative ROI progression ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Calculate ROI over different timeframes -- @1.4 " status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_timeframes
  Scenario Outline: Calculate ROI over different timeframes -- @1.4 
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to ROI calculation features ... skipped in 0.000s
    Given I have a security investment with 10 year lifespan ... skipped in 0.000s
    When I calculate the ROI ... skipped in 0.000s
    Then the calculation should account for the full 10 year period ... skipped in 0.000s
    And I should see year-by-year breakdown ... skipped in 0.000s
    And I should see cumulative ROI progression ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Apply discount rate for NPV calculation" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_discount_rate
  Scenario: Apply discount rate for NPV calculation
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to ROI calculation features ... skipped in 0.000s
    Given I have a ROI calculation ... skipped in 0.000s
    When I apply a discount rate of 8% ... skipped in 0.000s
    Then the NPV should be calculated using the discount rate ... skipped in 0.000s
    And future cash flows should be properly discounted ... skipped in 0.000s
    And I should see the impact of the discount rate on ROI ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Perform ROI sensitivity analysis" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_sensitivity
  Scenario: Perform ROI sensitivity analysis
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to ROI calculation features ... skipped in 0.000s
    Given I have a completed ROI calculation ... skipped in 0.000s
    When I perform sensitivity analysis on key parameters ... skipped in 0.000s
    Then I should see how ROI changes with parameter variations ... skipped in 0.000s
    And I should identify the most sensitive parameters ... skipped in 0.000s
    And I should see break-even analysis ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Compare multiple security investments" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_comparison
  Scenario: Compare multiple security investments
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to ROI calculation features ... skipped in 0.000s
    Given I have ROI calculations for multiple security tools ... skipped in 0.000s
    When I request a comparison analysis ... skipped in 0.000s
    Then I should see side-by-side ROI comparisons ... skipped in 0.000s
    And I should see ranking by ROI percentage ... skipped in 0.000s
    And I should see ranking by payback period ... skipped in 0.000s
    And I should see risk-adjusted comparisons ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Compare against industry benchmarks" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_industry_benchmarks
  Scenario: Compare against industry benchmarks
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to ROI calculation features ... skipped in 0.000s
    Given I have a ROI calculation for my organization ... skipped in 0.000s
    When I request industry benchmark comparison ... skipped in 0.000s
    Then I should see how my ROI compares to industry averages ... skipped in 0.000s
    And I should see percentile rankings ... skipped in 0.000s
    And I should see recommendations for improvement ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Apply risk adjustments to ROI" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_risk_adjustment
  Scenario: Apply risk adjustments to ROI
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to ROI calculation features ... skipped in 0.000s
    Given I have a basic ROI calculation ... skipped in 0.000s
    When I apply risk adjustments based on implementation uncertainty ... skipped in 0.000s
    Then I should see risk-adjusted ROI values ... skipped in 0.000s
    And I should see confidence intervals around the ROI ... skipped in 0.000s
    And I should see probability of achieving target ROI ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Include compliance benefits in ROI" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_compliance
  Scenario: Include compliance benefits in ROI
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to ROI calculation features ... skipped in 0.000s
    Given I have a security investment that helps with compliance ... skipped in 0.000s
    When I calculate ROI including compliance benefits ... skipped in 0.000s
    Then I should see avoided compliance penalties ... skipped in 0.000s
    And I should see reduced audit costs ... skipped in 0.000s
    And I should see faster compliance certification benefits ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Calculate incident prevention benefits" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_incident_prevention
  Scenario: Calculate incident prevention benefits
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to ROI calculation features ... skipped in 0.000s
    Given I have historical incident data ... skipped in 0.000s
    When I calculate ROI for a preventive security measure ... skipped in 0.000s
    Then I should see estimated incident prevention benefits ... skipped in 0.000s
    And I should see reduced incident response costs ... skipped in 0.000s
    And I should see business continuity improvements ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Include productivity gains in ROI" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_productivity
  Scenario: Include productivity gains in ROI
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to ROI calculation features ... skipped in 0.000s
    Given I have a security tool that improves analyst productivity ... skipped in 0.000s
    When I calculate the ROI ... skipped in 0.000s
    Then I should see time savings quantified ... skipped in 0.000s
    And I should see reduced manual effort costs ... skipped in 0.000s
    And I should see improved response time benefits ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Validate ROI calculation inputs" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_validation
  Scenario: Validate ROI calculation inputs
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to ROI calculation features ... skipped in 0.000s
    Given I want to create a ROI calculation ... skipped in 0.000s
    When I provide invalid input values ... skipped in 0.000s
    Then the system should validate the inputs ... skipped in 0.000s
    And I should receive specific error messages for invalid fields ... skipped in 0.000s
    And I should be guided to provide correct values ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Use ROI calculation templates" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_templates
  Scenario: Use ROI calculation templates
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to ROI calculation features ... skipped in 0.000s
    Given I want to calculate ROI for a common security tool type ... skipped in 0.000s
    When I select a pre-built template ... skipped in 0.000s
    Then the template should pre-populate relevant fields ... skipped in 0.000s
    And I should be able to customize the template ... skipped in 0.000s
    And I should see industry-standard assumptions ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Export ROI analysis for presentation" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_export
  Scenario: Export ROI analysis for presentation
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to ROI calculation features ... skipped in 0.000s
    Given I have completed a ROI calculation ... skipped in 0.000s
    When I export the analysis ... skipped in 0.000s
    Then I should be able to export in executive summary format ... skipped in 0.000s
    And I should be able to export detailed technical analysis ... skipped in 0.000s
    And I should be able to export charts and graphs ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Track actual vs projected ROI" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_tracking
  Scenario: Track actual vs projected ROI
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to ROI calculation features ... skipped in 0.000s
    Given I have implemented a security investment ... skipped in 0.000s
    When I track the actual performance against projections ... skipped in 0.000s
    Then I should be able to update actual costs and benefits ... skipped in 0.000s
    And I should see variance analysis ... skipped in 0.000s
    And I should see lessons learned for future calculations ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Submit ROI calculation for approval" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_approval_workflow
  Scenario: Submit ROI calculation for approval
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to ROI calculation features ... skipped in 0.000s
    Given I have completed a ROI calculation ... skipped in 0.000s
    When I submit it for management approval ... skipped in 0.000s
    Then it should enter the approval workflow ... skipped in 0.000s
    And stakeholders should be notified ... skipped in 0.000s
    And I should be able to track approval status ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Collaborate on ROI calculations" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_collaboration
  Scenario: Collaborate on ROI calculations
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to ROI calculation features ... skipped in 0.000s
    Given I have a ROI calculation in progress ... skipped in 0.000s
    When I share it with team members ... skipped in 0.000s
    Then they should be able to view and comment ... skipped in 0.000s
    And they should be able to suggest modifications ... skipped in 0.000s
    And I should see all collaboration history ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Maintain ROI calculation versions" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_version_control
  Scenario: Maintain ROI calculation versions
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to ROI calculation features ... skipped in 0.000s
    Given I have a ROI calculation ... skipped in 0.000s
    When I make modifications to the calculation ... skipped in 0.000s
    Then the system should maintain version history ... skipped in 0.000s
    And I should be able to compare versions ... skipped in 0.000s
    And I should be able to revert to previous versions ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Integrate ROI with enhanced risk calculations" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_integration
  Scenario: Integrate ROI with enhanced risk calculations
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to ROI calculation features ... skipped in 0.000s
    Given I have a basic ROI calculation ... skipped in 0.000s
    When I enhance it with Monte Carlo risk analysis ... skipped in 0.000s
    Then the enhanced calculation should reference the base ROI ... skipped in 0.000s
    And I should see risk-adjusted ROI values ... skipped in 0.000s
    And I should maintain traceability between calculations ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase></testsuite>