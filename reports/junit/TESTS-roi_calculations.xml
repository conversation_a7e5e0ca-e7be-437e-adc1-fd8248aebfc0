<testsuite name="roi_calculations.ROI Calculations for Security Investments" tests="23" errors="0" failures="20" skipped="23" time="0.0" timestamp="2025-06-20T16:47:21.262775" hostname="ganymede"><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Create basic ROI calculation" status="untested" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_basic
  Scenario: Create basic ROI calculation
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to ROI calculation features ... untested in 0.000s
    Given I want to calculate ROI for a security investment ... untested in 0.000s
    When I provide the following investment details ... untested in 0.000s
      | field                     | value     |
      | investment_name           | SIEM Tool |
      | initial_investment        | 500000    |
      | annual_operating_cost     | 100000    |
      | implementation_time       | 6         |
      | expected_lifespan         | 5         |
      | risk_reduction_percentage | 25        |
    Then the ROI calculation should be created successfully ... untested in 0.000s
    And I should receive the calculated ROI percentage ... untested in 0.000s
    And I should see the payback period ... untested in 0.000s
    And I should see the net present value ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Calculate security investment benefits" status="untested" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_benefits
  Scenario: Calculate security investment benefits
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to ROI calculation features ... untested in 0.000s
    Given I have a ROI calculation for a security tool ... untested in 0.000s
    When the system calculates the benefits ... untested in 0.000s
    Then I should see quantified risk reduction benefits ... untested in 0.000s
    And I should see operational efficiency gains ... untested in 0.000s
    And I should see compliance cost savings ... untested in 0.000s
    And I should see incident response cost reductions ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Calculate total cost of ownership" status="untested" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @roi_costs
  Scenario: Calculate total cost of ownership
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to ROI calculation features ... untested in 0.000s
    Given I have a security investment proposal ... untested in 0.000s
    When I calculate the total costs ... untested in 0.000s
    Then I should see initial purchase costs ... untested in 0.000s
    And I should see implementation costs ... untested in 0.000s
    And I should see annual licensing costs ... untested in 0.000s
    And I should see training and maintenance costs ... untested in 0.000s
    And I should see opportunity costs ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Calculate ROI over different timeframes -- @1.1 " status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a security investment with 3 year lifespan" /><system-out>
<![CDATA[
@scenario.begin

  @roi_timeframes
  Scenario Outline: Calculate ROI over different timeframes -- @1.1 
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to ROI calculation features ... untested in 0.000s
    Given I have a security investment with 3 year lifespan ... undefined in 0.000s
    When I calculate the ROI ... untested in 0.000s
    Then the calculation should account for the full 3 year period ... undefined in 0.000s
    And I should see year-by-year breakdown ... untested in 0.000s
    And I should see cumulative ROI progression ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Calculate ROI over different timeframes -- @1.2 " status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a security investment with 5 year lifespan" /><system-out>
<![CDATA[
@scenario.begin

  @roi_timeframes
  Scenario Outline: Calculate ROI over different timeframes -- @1.2 
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to ROI calculation features ... untested in 0.000s
    Given I have a security investment with 5 year lifespan ... undefined in 0.000s
    When I calculate the ROI ... untested in 0.000s
    Then the calculation should account for the full 5 year period ... undefined in 0.000s
    And I should see year-by-year breakdown ... untested in 0.000s
    And I should see cumulative ROI progression ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Calculate ROI over different timeframes -- @1.3 " status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a security investment with 7 year lifespan" /><system-out>
<![CDATA[
@scenario.begin

  @roi_timeframes
  Scenario Outline: Calculate ROI over different timeframes -- @1.3 
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to ROI calculation features ... untested in 0.000s
    Given I have a security investment with 7 year lifespan ... undefined in 0.000s
    When I calculate the ROI ... untested in 0.000s
    Then the calculation should account for the full 7 year period ... undefined in 0.000s
    And I should see year-by-year breakdown ... untested in 0.000s
    And I should see cumulative ROI progression ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Calculate ROI over different timeframes -- @1.4 " status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a security investment with 10 year lifespan" /><system-out>
<![CDATA[
@scenario.begin

  @roi_timeframes
  Scenario Outline: Calculate ROI over different timeframes -- @1.4 
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to ROI calculation features ... untested in 0.000s
    Given I have a security investment with 10 year lifespan ... undefined in 0.000s
    When I calculate the ROI ... untested in 0.000s
    Then the calculation should account for the full 10 year period ... undefined in 0.000s
    And I should see year-by-year breakdown ... untested in 0.000s
    And I should see cumulative ROI progression ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Apply discount rate for NPV calculation" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a ROI calculation" /><system-out>
<![CDATA[
@scenario.begin

  @roi_discount_rate
  Scenario: Apply discount rate for NPV calculation
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to ROI calculation features ... untested in 0.000s
    Given I have a ROI calculation ... undefined in 0.000s
    When I apply a discount rate of 8% ... undefined in 0.000s
    Then the NPV should be calculated using the discount rate ... undefined in 0.000s
    And future cash flows should be properly discounted ... undefined in 0.000s
    And I should see the impact of the discount rate on ROI ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Perform ROI sensitivity analysis" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a completed ROI calculation" /><system-out>
<![CDATA[
@scenario.begin

  @roi_sensitivity
  Scenario: Perform ROI sensitivity analysis
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to ROI calculation features ... untested in 0.000s
    Given I have a completed ROI calculation ... undefined in 0.000s
    When I perform sensitivity analysis on key parameters ... undefined in 0.000s
    Then I should see how ROI changes with parameter variations ... undefined in 0.000s
    And I should identify the most sensitive parameters ... undefined in 0.000s
    And I should see break-even analysis ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Compare multiple security investments" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have ROI calculations for multiple security tools" /><system-out>
<![CDATA[
@scenario.begin

  @roi_comparison
  Scenario: Compare multiple security investments
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to ROI calculation features ... untested in 0.000s
    Given I have ROI calculations for multiple security tools ... undefined in 0.000s
    When I request a comparison analysis ... undefined in 0.000s
    Then I should see side-by-side ROI comparisons ... undefined in 0.000s
    And I should see ranking by ROI percentage ... undefined in 0.000s
    And I should see ranking by payback period ... undefined in 0.000s
    And I should see risk-adjusted comparisons ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Compare against industry benchmarks" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a ROI calculation for my organization" /><system-out>
<![CDATA[
@scenario.begin

  @roi_industry_benchmarks
  Scenario: Compare against industry benchmarks
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to ROI calculation features ... untested in 0.000s
    Given I have a ROI calculation for my organization ... undefined in 0.000s
    When I request industry benchmark comparison ... undefined in 0.000s
    Then I should see how my ROI compares to industry averages ... undefined in 0.000s
    And I should see percentile rankings ... undefined in 0.000s
    And I should see recommendations for improvement ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Apply risk adjustments to ROI" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a basic ROI calculation" /><system-out>
<![CDATA[
@scenario.begin

  @roi_risk_adjustment
  Scenario: Apply risk adjustments to ROI
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to ROI calculation features ... untested in 0.000s
    Given I have a basic ROI calculation ... undefined in 0.000s
    When I apply risk adjustments based on implementation uncertainty ... undefined in 0.000s
    Then I should see risk-adjusted ROI values ... undefined in 0.000s
    And I should see confidence intervals around the ROI ... undefined in 0.000s
    And I should see probability of achieving target ROI ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Include compliance benefits in ROI" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a security investment that helps with compliance" /><system-out>
<![CDATA[
@scenario.begin

  @roi_compliance
  Scenario: Include compliance benefits in ROI
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to ROI calculation features ... untested in 0.000s
    Given I have a security investment that helps with compliance ... undefined in 0.000s
    When I calculate ROI including compliance benefits ... undefined in 0.000s
    Then I should see avoided compliance penalties ... undefined in 0.000s
    And I should see reduced audit costs ... undefined in 0.000s
    And I should see faster compliance certification benefits ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Calculate incident prevention benefits" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have historical incident data" /><system-out>
<![CDATA[
@scenario.begin

  @roi_incident_prevention
  Scenario: Calculate incident prevention benefits
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to ROI calculation features ... untested in 0.000s
    Given I have historical incident data ... undefined in 0.000s
    When I calculate ROI for a preventive security measure ... undefined in 0.000s
    Then I should see estimated incident prevention benefits ... undefined in 0.000s
    And I should see reduced incident response costs ... undefined in 0.000s
    And I should see business continuity improvements ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Include productivity gains in ROI" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a security tool that improves analyst productivity" /><system-out>
<![CDATA[
@scenario.begin

  @roi_productivity
  Scenario: Include productivity gains in ROI
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to ROI calculation features ... untested in 0.000s
    Given I have a security tool that improves analyst productivity ... undefined in 0.000s
    When I calculate the ROI ... untested in 0.000s
    Then I should see time savings quantified ... undefined in 0.000s
    And I should see reduced manual effort costs ... undefined in 0.000s
    And I should see improved response time benefits ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Validate ROI calculation inputs" status="untested" time="0"><failure type="undefined" message="Undefined Step: I want to create a ROI calculation" /><system-out>
<![CDATA[
@scenario.begin

  @roi_validation
  Scenario: Validate ROI calculation inputs
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to ROI calculation features ... untested in 0.000s
    Given I want to create a ROI calculation ... undefined in 0.000s
    When I provide invalid input values ... undefined in 0.000s
    Then the system should validate the inputs ... undefined in 0.000s
    And I should receive specific error messages for invalid fields ... undefined in 0.000s
    And I should be guided to provide correct values ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Use ROI calculation templates" status="untested" time="0"><failure type="undefined" message="Undefined Step: I want to calculate ROI for a common security tool type" /><system-out>
<![CDATA[
@scenario.begin

  @roi_templates
  Scenario: Use ROI calculation templates
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to ROI calculation features ... untested in 0.000s
    Given I want to calculate ROI for a common security tool type ... undefined in 0.000s
    When I select a pre-built template ... undefined in 0.000s
    Then the template should pre-populate relevant fields ... undefined in 0.000s
    And I should be able to customize the template ... undefined in 0.000s
    And I should see industry-standard assumptions ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Export ROI analysis for presentation" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have completed a ROI calculation" /><system-out>
<![CDATA[
@scenario.begin

  @roi_export
  Scenario: Export ROI analysis for presentation
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to ROI calculation features ... untested in 0.000s
    Given I have completed a ROI calculation ... undefined in 0.000s
    When I export the analysis ... undefined in 0.000s
    Then I should be able to export in executive summary format ... undefined in 0.000s
    And I should be able to export detailed technical analysis ... undefined in 0.000s
    And I should be able to export charts and graphs ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Track actual vs projected ROI" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have implemented a security investment" /><system-out>
<![CDATA[
@scenario.begin

  @roi_tracking
  Scenario: Track actual vs projected ROI
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to ROI calculation features ... untested in 0.000s
    Given I have implemented a security investment ... undefined in 0.000s
    When I track the actual performance against projections ... undefined in 0.000s
    Then I should be able to update actual costs and benefits ... undefined in 0.000s
    And I should see variance analysis ... undefined in 0.000s
    And I should see lessons learned for future calculations ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Submit ROI calculation for approval" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have completed a ROI calculation" /><system-out>
<![CDATA[
@scenario.begin

  @roi_approval_workflow
  Scenario: Submit ROI calculation for approval
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to ROI calculation features ... untested in 0.000s
    Given I have completed a ROI calculation ... undefined in 0.000s
    When I submit it for management approval ... undefined in 0.000s
    Then it should enter the approval workflow ... undefined in 0.000s
    And stakeholders should be notified ... undefined in 0.000s
    And I should be able to track approval status ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Collaborate on ROI calculations" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a ROI calculation in progress" /><system-out>
<![CDATA[
@scenario.begin

  @roi_collaboration
  Scenario: Collaborate on ROI calculations
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to ROI calculation features ... untested in 0.000s
    Given I have a ROI calculation in progress ... undefined in 0.000s
    When I share it with team members ... undefined in 0.000s
    Then they should be able to view and comment ... undefined in 0.000s
    And they should be able to suggest modifications ... undefined in 0.000s
    And I should see all collaboration history ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Maintain ROI calculation versions" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a ROI calculation" /><system-out>
<![CDATA[
@scenario.begin

  @roi_version_control
  Scenario: Maintain ROI calculation versions
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to ROI calculation features ... untested in 0.000s
    Given I have a ROI calculation ... undefined in 0.000s
    When I make modifications to the calculation ... undefined in 0.000s
    Then the system should maintain version history ... undefined in 0.000s
    And I should be able to compare versions ... undefined in 0.000s
    And I should be able to revert to previous versions ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="roi_calculations.ROI Calculations for Security Investments" name="Integrate ROI with enhanced risk calculations" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a basic ROI calculation" /><system-out>
<![CDATA[
@scenario.begin

  @roi_integration
  Scenario: Integrate ROI with enhanced risk calculations
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to ROI calculation features ... untested in 0.000s
    Given I have a basic ROI calculation ... undefined in 0.000s
    When I enhance it with Monte Carlo risk analysis ... undefined in 0.000s
    Then the enhanced calculation should reference the base ROI ... undefined in 0.000s
    And I should see risk-adjusted ROI values ... undefined in 0.000s
    And I should maintain traceability between calculations ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase></testsuite>