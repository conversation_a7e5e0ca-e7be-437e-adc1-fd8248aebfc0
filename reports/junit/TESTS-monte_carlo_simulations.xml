<testsuite name="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" tests="22" errors="0" failures="0" skipped="22" time="0.0" timestamp="2025-06-20T17:13:43.063300" hostname="ganymede"><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Create basic Monte Carlo simulation" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_basic
  Scenario: Create basic Monte Carlo simulation
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to Monte Carlo simulation features ... skipped in 0.000s
    Given I have a base ROI calculation ... skipped in 0.000s
    And I have a risk profile for my organization ... skipped in 0.000s
    When I create a Monte Carlo simulation with the following parameters ... skipped in 0.000s
      | parameter_name     | distribution | mean | std  | min     | max      |
      | breach_cost        | lognormal    | 15.4 | 0.5  | 1000000 | 50000000 |
      | breach_probability | beta         | 2    | 5    | 0.05    | 0.95     |
      | risk_reduction     | triangular   | 0.15 | 0.25 | 0.35    |          |
    And I set the simulation to run 10000 iterations ... skipped in 0.000s
    Then the simulation should complete successfully ... skipped in 0.000s
    And I should receive statistical results ... skipped in 0.000s
    And the simulation results should include confidence intervals ... skipped in 0.000s
    And the simulation should converge within the iteration limit ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Test different probability distributions -- @1.1 " status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_distributions
  Scenario Outline: Test different probability distributions -- @1.1 
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to Monte Carlo simulation features ... skipped in 0.000s
    Given I have a Monte Carlo simulation setup ... skipped in 0.000s
    When I configure a parameter with "normal" distribution ... skipped in 0.000s
    And I provide the required parameters for "normal" ... skipped in 0.000s
    Then the parameter should be validated successfully ... skipped in 0.000s
    And the distribution should generate valid samples ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Test different probability distributions -- @1.2 " status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_distributions
  Scenario Outline: Test different probability distributions -- @1.2 
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to Monte Carlo simulation features ... skipped in 0.000s
    Given I have a Monte Carlo simulation setup ... skipped in 0.000s
    When I configure a parameter with "lognormal" distribution ... skipped in 0.000s
    And I provide the required parameters for "lognormal" ... skipped in 0.000s
    Then the parameter should be validated successfully ... skipped in 0.000s
    And the distribution should generate valid samples ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Test different probability distributions -- @1.3 " status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_distributions
  Scenario Outline: Test different probability distributions -- @1.3 
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to Monte Carlo simulation features ... skipped in 0.000s
    Given I have a Monte Carlo simulation setup ... skipped in 0.000s
    When I configure a parameter with "beta" distribution ... skipped in 0.000s
    And I provide the required parameters for "beta" ... skipped in 0.000s
    Then the parameter should be validated successfully ... skipped in 0.000s
    And the distribution should generate valid samples ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Test different probability distributions -- @1.4 " status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_distributions
  Scenario Outline: Test different probability distributions -- @1.4 
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to Monte Carlo simulation features ... skipped in 0.000s
    Given I have a Monte Carlo simulation setup ... skipped in 0.000s
    When I configure a parameter with "triangular" distribution ... skipped in 0.000s
    And I provide the required parameters for "triangular" ... skipped in 0.000s
    Then the parameter should be validated successfully ... skipped in 0.000s
    And the distribution should generate valid samples ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Test different probability distributions -- @1.5 " status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_distributions
  Scenario Outline: Test different probability distributions -- @1.5 
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to Monte Carlo simulation features ... skipped in 0.000s
    Given I have a Monte Carlo simulation setup ... skipped in 0.000s
    When I configure a parameter with "uniform" distribution ... skipped in 0.000s
    And I provide the required parameters for "uniform" ... skipped in 0.000s
    Then the parameter should be validated successfully ... skipped in 0.000s
    And the distribution should generate valid samples ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Reject simulation with insufficient iterations" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_validation
  Scenario: Reject simulation with insufficient iterations
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to Monte Carlo simulation features ... skipped in 0.000s
    Given I have a Monte Carlo simulation setup ... skipped in 0.000s
    When I set the iteration count to 500 ... skipped in 0.000s
    Then the simulation should be rejected ... skipped in 0.000s
    And I should receive an error about minimum iteration requirements ... skipped in 0.000s
    And the minimum should be 1000 iterations ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Validate distribution parameters" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_validation
  Scenario: Validate distribution parameters
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to Monte Carlo simulation features ... skipped in 0.000s
    Given I want to create a Monte Carlo parameter ... skipped in 0.000s
    When I specify a "normal" distribution without "std" parameter ... skipped in 0.000s
    Then the parameter should be rejected ... skipped in 0.000s
    And I should receive a validation error about missing standard deviation ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Large simulation completes within time limit" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_performance
  Scenario: Large simulation completes within time limit
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to Monte Carlo simulation features ... skipped in 0.000s
    Given I have a Monte Carlo simulation with 50000 iterations ... skipped in 0.000s
    When I run the simulation ... skipped in 0.000s
    Then it should complete within 60 seconds ... skipped in 0.000s
    And the results should be statistically valid ... skipped in 0.000s
    And convergence should be achieved ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Simulation achieves convergence" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_convergence
  Scenario: Simulation achieves convergence
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to Monte Carlo simulation features ... skipped in 0.000s
    Given I have a Monte Carlo simulation running ... skipped in 0.000s
    When the simulation reaches convergence criteria ... skipped in 0.000s
    Then the simulation should stop automatically ... skipped in 0.000s
    And I should receive a convergence notification ... skipped in 0.000s
    And the final iteration count should be reported ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Perform sensitivity analysis" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_sensitivity
  Scenario: Perform sensitivity analysis
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to Monte Carlo simulation features ... skipped in 0.000s
    Given I have completed a Monte Carlo simulation ... skipped in 0.000s
    When I request sensitivity analysis ... skipped in 0.000s
    Then I should receive parameter sensitivity rankings ... skipped in 0.000s
    And the most influential parameters should be identified ... skipped in 0.000s
    And correlation coefficients should be provided ... skipped in 0.000s
    And tornado chart data should be available ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Calculate Value at Risk metrics" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_var
  Scenario: Calculate Value at Risk metrics
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to Monte Carlo simulation features ... skipped in 0.000s
    Given I have completed a Monte Carlo simulation ... skipped in 0.000s
    When I analyze the risk metrics ... skipped in 0.000s
    Then I should receive VaR at 95% confidence level ... skipped in 0.000s
    And I should receive VaR at 99% confidence level ... skipped in 0.000s
    And I should receive Conditional VaR (Expected Shortfall) ... skipped in 0.000s
    And all VaR values should be mathematically consistent ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Generate confidence intervals" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_confidence_intervals
  Scenario: Generate confidence intervals
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to Monte Carlo simulation features ... skipped in 0.000s
    Given I have completed a Monte Carlo simulation ... skipped in 0.000s
    When I request confidence interval analysis ... skipped in 0.000s
    Then I should receive intervals at key percentiles ... skipped in 0.000s
      | percentile | description    |
      | 5          | Worst case     |
      | 25         | Lower quartile |
      | 50         | Median         |
      | 75         | Upper quartile |
      | 95         | Best case      |
    And the intervals should be in ascending order ... skipped in 0.000s
    And the values should be realistic for the input parameters ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Reproducible results with random seed" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_random_seed
  Scenario: Reproducible results with random seed
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to Monte Carlo simulation features ... skipped in 0.000s
    Given I have a Monte Carlo simulation with random seed 42 ... skipped in 0.000s
    When I run the simulation twice with the same seed ... skipped in 0.000s
    Then both runs should produce identical results ... skipped in 0.000s
    And the statistical measures should match exactly ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Handle invalid parameter combinations" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_error_handling
  Scenario: Handle invalid parameter combinations
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to Monte Carlo simulation features ... skipped in 0.000s
    Given I want to create a Monte Carlo simulation ... skipped in 0.000s
    When I provide parameters that would cause mathematical errors ... skipped in 0.000s
    Then the simulation should detect the invalid configuration ... skipped in 0.000s
    And I should receive a descriptive error message ... skipped in 0.000s
    And the simulation should not start ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Handle large simulations efficiently" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_memory
  Scenario: Handle large simulations efficiently
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to Monte Carlo simulation features ... skipped in 0.000s
    Given I have a Monte Carlo simulation with 100000 iterations ... skipped in 0.000s
    When I run the simulation ... skipped in 0.000s
    Then the memory usage should remain within acceptable limits ... skipped in 0.000s
    And the simulation should not cause system performance issues ... skipped in 0.000s
    And results should be processed incrementally ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Parallel processing for large simulations" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_parallel
  Scenario: Parallel processing for large simulations
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to Monte Carlo simulation features ... skipped in 0.000s
    Given I have a Monte Carlo simulation with 50000 iterations ... skipped in 0.000s
    When the simulation runs with parallel processing enabled ... skipped in 0.000s
    Then it should complete faster than sequential processing ... skipped in 0.000s
    And the results should be identical to sequential processing ... skipped in 0.000s
    And all CPU cores should be utilized efficiently ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Export simulation results" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_export
  Scenario: Export simulation results
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to Monte Carlo simulation features ... skipped in 0.000s
    Given I have completed a Monte Carlo simulation ... skipped in 0.000s
    When I request to export the results ... skipped in 0.000s
    Then I should be able to export in multiple formats ... skipped in 0.000s
      | format | description        |
      | JSON   | Machine readable   |
      | CSV    | Spreadsheet import |
      | PDF    | Executive summary  |
    And each export should contain all relevant statistics ... skipped in 0.000s
    And the data should be properly formatted ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Compare multiple simulation scenarios" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_comparison
  Scenario: Compare multiple simulation scenarios
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to Monte Carlo simulation features ... skipped in 0.000s
    Given I have completed multiple Monte Carlo simulations ... skipped in 0.000s
    When I request a comparison analysis ... skipped in 0.000s
    Then I should see side-by-side statistics ... skipped in 0.000s
    And I should see relative differences between scenarios ... skipped in 0.000s
    And I should be able to identify the best and worst case scenarios ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Use historical data for parameter estimation" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_historical
  Scenario: Use historical data for parameter estimation
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to Monte Carlo simulation features ... skipped in 0.000s
    Given I have historical breach cost data ... skipped in 0.000s
    When I use it to estimate distribution parameters ... skipped in 0.000s
    Then the system should suggest appropriate distributions ... skipped in 0.000s
    And the parameters should fit the historical data ... skipped in 0.000s
    And I should see goodness-of-fit statistics ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Monitor simulation progress in real-time" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_real_time
  Scenario: Monitor simulation progress in real-time
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to Monte Carlo simulation features ... skipped in 0.000s
    Given I have started a long-running Monte Carlo simulation ... skipped in 0.000s
    When I check the simulation status ... skipped in 0.000s
    Then I should see current progress percentage ... skipped in 0.000s
    And I should see estimated time to completion ... skipped in 0.000s
    And I should be able to cancel the simulation if needed ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Run multiple simulations in batch" status="skipped" time="0"><skipped /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_batch
  Scenario: Run multiple simulations in batch
    Given I am logged in as a security analyst ... skipped in 0.000s
    And I have access to Monte Carlo simulation features ... skipped in 0.000s
    Given I have multiple simulation configurations ... skipped in 0.000s
    When I submit them as a batch job ... skipped in 0.000s
    Then all simulations should run sequentially ... skipped in 0.000s
    And I should receive notifications as each completes ... skipped in 0.000s
    And I should get a summary report of all results ... skipped in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase></testsuite>