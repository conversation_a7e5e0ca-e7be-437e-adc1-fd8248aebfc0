<testsuite name="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" tests="22" errors="0" failures="22" skipped="22" time="0.0" timestamp="2025-06-20T16:47:21.255985" hostname="ganymede"><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Create basic Monte Carlo simulation" status="untested" time="0"><failure type="undefined" message="Undefined Step: I set the simulation to run 10000 iterations" /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_basic
  Scenario: Create basic Monte Carlo simulation
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to Monte Carlo simulation features ... untested in 0.000s
    Given I have a base ROI calculation ... untested in 0.000s
    And I have a risk profile for my organization ... untested in 0.000s
    When I create a Monte Carlo simulation with the following parameters ... untested in 0.000s
      | parameter_name     | distribution | mean | std  | min     | max      |
      | breach_cost        | lognormal    | 15.4 | 0.5  | 1000000 | 50000000 |
      | breach_probability | beta         | 2    | 5    | 0.05    | 0.95     |
      | risk_reduction     | triangular   | 0.15 | 0.25 | 0.35    |          |
    And I set the simulation to run 10000 iterations ... undefined in 0.000s
    Then the simulation should complete successfully ... untested in 0.000s
    And I should receive statistical results ... untested in 0.000s
    And the simulation results should include confidence intervals ... untested in 0.000s
    And the simulation should converge within the iteration limit ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Test different probability distributions -- @1.1 " status="untested" time="0"><failure type="undefined" message="Undefined Step: I configure a parameter with &quot;normal&quot; distribution" /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_distributions
  Scenario Outline: Test different probability distributions -- @1.1 
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to Monte Carlo simulation features ... untested in 0.000s
    Given I have a Monte Carlo simulation setup ... untested in 0.000s
    When I configure a parameter with "normal" distribution ... undefined in 0.000s
    And I provide the required parameters for "normal" ... undefined in 0.000s
    Then the parameter should be validated successfully ... untested in 0.000s
    And the distribution should generate valid samples ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Test different probability distributions -- @1.2 " status="untested" time="0"><failure type="undefined" message="Undefined Step: I configure a parameter with &quot;lognormal&quot; distribution" /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_distributions
  Scenario Outline: Test different probability distributions -- @1.2 
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to Monte Carlo simulation features ... untested in 0.000s
    Given I have a Monte Carlo simulation setup ... untested in 0.000s
    When I configure a parameter with "lognormal" distribution ... undefined in 0.000s
    And I provide the required parameters for "lognormal" ... undefined in 0.000s
    Then the parameter should be validated successfully ... untested in 0.000s
    And the distribution should generate valid samples ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Test different probability distributions -- @1.3 " status="untested" time="0"><failure type="undefined" message="Undefined Step: I configure a parameter with &quot;beta&quot; distribution" /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_distributions
  Scenario Outline: Test different probability distributions -- @1.3 
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to Monte Carlo simulation features ... untested in 0.000s
    Given I have a Monte Carlo simulation setup ... untested in 0.000s
    When I configure a parameter with "beta" distribution ... undefined in 0.000s
    And I provide the required parameters for "beta" ... undefined in 0.000s
    Then the parameter should be validated successfully ... untested in 0.000s
    And the distribution should generate valid samples ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Test different probability distributions -- @1.4 " status="untested" time="0"><failure type="undefined" message="Undefined Step: I configure a parameter with &quot;triangular&quot; distribution" /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_distributions
  Scenario Outline: Test different probability distributions -- @1.4 
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to Monte Carlo simulation features ... untested in 0.000s
    Given I have a Monte Carlo simulation setup ... untested in 0.000s
    When I configure a parameter with "triangular" distribution ... undefined in 0.000s
    And I provide the required parameters for "triangular" ... undefined in 0.000s
    Then the parameter should be validated successfully ... untested in 0.000s
    And the distribution should generate valid samples ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Test different probability distributions -- @1.5 " status="untested" time="0"><failure type="undefined" message="Undefined Step: I configure a parameter with &quot;uniform&quot; distribution" /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_distributions
  Scenario Outline: Test different probability distributions -- @1.5 
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to Monte Carlo simulation features ... untested in 0.000s
    Given I have a Monte Carlo simulation setup ... untested in 0.000s
    When I configure a parameter with "uniform" distribution ... undefined in 0.000s
    And I provide the required parameters for "uniform" ... undefined in 0.000s
    Then the parameter should be validated successfully ... untested in 0.000s
    And the distribution should generate valid samples ... untested in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Reject simulation with insufficient iterations" status="untested" time="0"><failure type="undefined" message="Undefined Step: I set the iteration count to 500" /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_validation
  Scenario: Reject simulation with insufficient iterations
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to Monte Carlo simulation features ... untested in 0.000s
    Given I have a Monte Carlo simulation setup ... untested in 0.000s
    When I set the iteration count to 500 ... undefined in 0.000s
    Then the simulation should be rejected ... untested in 0.000s
    And I should receive an error about minimum iteration requirements ... untested in 0.000s
    And the minimum should be 1000 iterations ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Validate distribution parameters" status="untested" time="0"><failure type="undefined" message="Undefined Step: I want to create a Monte Carlo parameter" /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_validation
  Scenario: Validate distribution parameters
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to Monte Carlo simulation features ... untested in 0.000s
    Given I want to create a Monte Carlo parameter ... undefined in 0.000s
    When I specify a "normal" distribution without "std" parameter ... undefined in 0.000s
    Then the parameter should be rejected ... undefined in 0.000s
    And I should receive a validation error about missing standard deviation ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Large simulation completes within time limit" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a Monte Carlo simulation with 50000 iterations" /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_performance
  Scenario: Large simulation completes within time limit
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to Monte Carlo simulation features ... untested in 0.000s
    Given I have a Monte Carlo simulation with 50000 iterations ... undefined in 0.000s
    When I run the simulation ... undefined in 0.000s
    Then it should complete within 60 seconds ... undefined in 0.000s
    And the results should be statistically valid ... undefined in 0.000s
    And convergence should be achieved ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Simulation achieves convergence" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a Monte Carlo simulation running" /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_convergence
  Scenario: Simulation achieves convergence
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to Monte Carlo simulation features ... untested in 0.000s
    Given I have a Monte Carlo simulation running ... undefined in 0.000s
    When the simulation reaches convergence criteria ... undefined in 0.000s
    Then the simulation should stop automatically ... undefined in 0.000s
    And I should receive a convergence notification ... undefined in 0.000s
    And the final iteration count should be reported ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Perform sensitivity analysis" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have completed a Monte Carlo simulation" /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_sensitivity
  Scenario: Perform sensitivity analysis
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to Monte Carlo simulation features ... untested in 0.000s
    Given I have completed a Monte Carlo simulation ... undefined in 0.000s
    When I request sensitivity analysis ... undefined in 0.000s
    Then I should receive parameter sensitivity rankings ... untested in 0.000s
    And the most influential parameters should be identified ... untested in 0.000s
    And correlation coefficients should be provided ... undefined in 0.000s
    And tornado chart data should be available ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Calculate Value at Risk metrics" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have completed a Monte Carlo simulation" /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_var
  Scenario: Calculate Value at Risk metrics
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to Monte Carlo simulation features ... untested in 0.000s
    Given I have completed a Monte Carlo simulation ... undefined in 0.000s
    When I analyze the risk metrics ... undefined in 0.000s
    Then I should receive VaR at 95% confidence level ... undefined in 0.000s
    And I should receive VaR at 99% confidence level ... undefined in 0.000s
    And I should receive Conditional VaR (Expected Shortfall) ... undefined in 0.000s
    And all VaR values should be mathematically consistent ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Generate confidence intervals" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have completed a Monte Carlo simulation" /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_confidence_intervals
  Scenario: Generate confidence intervals
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to Monte Carlo simulation features ... untested in 0.000s
    Given I have completed a Monte Carlo simulation ... undefined in 0.000s
    When I request confidence interval analysis ... undefined in 0.000s
    Then I should receive intervals at key percentiles ... undefined in 0.000s
      | percentile | description    |
      | 5          | Worst case     |
      | 25         | Lower quartile |
      | 50         | Median         |
      | 75         | Upper quartile |
      | 95         | Best case      |
    And the intervals should be in ascending order ... undefined in 0.000s
    And the values should be realistic for the input parameters ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Reproducible results with random seed" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a Monte Carlo simulation with random seed 42" /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_random_seed
  Scenario: Reproducible results with random seed
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to Monte Carlo simulation features ... untested in 0.000s
    Given I have a Monte Carlo simulation with random seed 42 ... undefined in 0.000s
    When I run the simulation twice with the same seed ... undefined in 0.000s
    Then both runs should produce identical results ... undefined in 0.000s
    And the statistical measures should match exactly ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Handle invalid parameter combinations" status="untested" time="0"><failure type="undefined" message="Undefined Step: I want to create a Monte Carlo simulation" /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_error_handling
  Scenario: Handle invalid parameter combinations
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to Monte Carlo simulation features ... untested in 0.000s
    Given I want to create a Monte Carlo simulation ... undefined in 0.000s
    When I provide parameters that would cause mathematical errors ... undefined in 0.000s
    Then the simulation should detect the invalid configuration ... undefined in 0.000s
    And I should receive a descriptive error message ... undefined in 0.000s
    And the simulation should not start ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Handle large simulations efficiently" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a Monte Carlo simulation with 100000 iterations" /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_memory
  Scenario: Handle large simulations efficiently
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to Monte Carlo simulation features ... untested in 0.000s
    Given I have a Monte Carlo simulation with 100000 iterations ... undefined in 0.000s
    When I run the simulation ... undefined in 0.000s
    Then the memory usage should remain within acceptable limits ... undefined in 0.000s
    And the simulation should not cause system performance issues ... undefined in 0.000s
    And results should be processed incrementally ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Parallel processing for large simulations" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have a Monte Carlo simulation with 50000 iterations" /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_parallel
  Scenario: Parallel processing for large simulations
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to Monte Carlo simulation features ... untested in 0.000s
    Given I have a Monte Carlo simulation with 50000 iterations ... undefined in 0.000s
    When the simulation runs with parallel processing enabled ... undefined in 0.000s
    Then it should complete faster than sequential processing ... undefined in 0.000s
    And the results should be identical to sequential processing ... undefined in 0.000s
    And all CPU cores should be utilized efficiently ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Export simulation results" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have completed a Monte Carlo simulation" /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_export
  Scenario: Export simulation results
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to Monte Carlo simulation features ... untested in 0.000s
    Given I have completed a Monte Carlo simulation ... undefined in 0.000s
    When I request to export the results ... undefined in 0.000s
    Then I should be able to export in multiple formats ... undefined in 0.000s
      | format | description        |
      | JSON   | Machine readable   |
      | CSV    | Spreadsheet import |
      | PDF    | Executive summary  |
    And each export should contain all relevant statistics ... undefined in 0.000s
    And the data should be properly formatted ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Compare multiple simulation scenarios" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have completed multiple Monte Carlo simulations" /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_comparison
  Scenario: Compare multiple simulation scenarios
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to Monte Carlo simulation features ... untested in 0.000s
    Given I have completed multiple Monte Carlo simulations ... undefined in 0.000s
    When I request a comparison analysis ... undefined in 0.000s
    Then I should see side-by-side statistics ... undefined in 0.000s
    And I should see relative differences between scenarios ... undefined in 0.000s
    And I should be able to identify the best and worst case scenarios ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Use historical data for parameter estimation" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have historical breach cost data" /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_historical
  Scenario: Use historical data for parameter estimation
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to Monte Carlo simulation features ... untested in 0.000s
    Given I have historical breach cost data ... undefined in 0.000s
    When I use it to estimate distribution parameters ... undefined in 0.000s
    Then the system should suggest appropriate distributions ... undefined in 0.000s
    And the parameters should fit the historical data ... undefined in 0.000s
    And I should see goodness-of-fit statistics ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Monitor simulation progress in real-time" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have started a long-running Monte Carlo simulation" /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_real_time
  Scenario: Monitor simulation progress in real-time
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to Monte Carlo simulation features ... untested in 0.000s
    Given I have started a long-running Monte Carlo simulation ... undefined in 0.000s
    When I check the simulation status ... undefined in 0.000s
    Then I should see current progress percentage ... undefined in 0.000s
    And I should see estimated time to completion ... undefined in 0.000s
    And I should be able to cancel the simulation if needed ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase><testcase classname="monte_carlo_simulations.Monte Carlo Simulations for Risk Analysis" name="Run multiple simulations in batch" status="untested" time="0"><failure type="undefined" message="Undefined Step: I have multiple simulation configurations" /><system-out>
<![CDATA[
@scenario.begin

  @monte_carlo_batch
  Scenario: Run multiple simulations in batch
    Given I am logged in as a security analyst ... untested in 0.000s
    And I have access to Monte Carlo simulation features ... untested in 0.000s
    Given I have multiple simulation configurations ... undefined in 0.000s
    When I submit them as a batch job ... undefined in 0.000s
    Then all simulations should run sequentially ... undefined in 0.000s
    And I should receive notifications as each completes ... undefined in 0.000s
    And I should get a summary report of all results ... undefined in 0.000s

@scenario.end
--------------------------------------------------------------------------------
]]>
</system-out></testcase></testsuite>