"""Step definitions for validation and error handling scenarios.

This module contains step implementations for validation and error handling
in Phase 1.3 Enhanced Risk & Cost Modeling BDD tests.
"""

import requests
from behave import given, when, then
from behave.runner import Context


# Validation Steps

@given('I want to create an enhanced risk calculation')
def step_want_create_enhanced_calculation(context: Context):
    """Initialize enhanced risk calculation creation."""
    context.enhanced_calc_data = {
        'name': 'Validation Test Calculation',
        'description': 'Testing validation scenarios'
    }


@when('I specify simulation iterations as "{iterations}"')
def step_specify_simulation_iterations(context: Context, iterations: str):
    """Specify simulation iterations for validation testing."""
    context.enhanced_calc_data['simulation_iterations'] = int(iterations)
    context.enhanced_calc_data.update({
        'base_calculation_id': 1,  # Mock ID
        'risk_profile_id': 1,      # Mock ID
        'monte_carlo_parameters': []
    })


@then('the system should reject the request')
def step_system_rejects_request(context: Context):
    """Verify the system rejects the request."""
    response = requests.post(
        f"{context.base_url}/api/v1/enhanced-risk/calculations",
        json=context.enhanced_calc_data,
        headers=context.auth_headers
    )
    
    assert response.status_code == 422  # Validation error
    context.validation_response = response.json()


@then('I should receive an error about minimum iteration requirements')
def step_error_minimum_iteration_requirements(context: Context):
    """Verify error message about minimum iterations."""
    error_detail = context.validation_response['detail']
    # Check if error mentions iterations or minimum requirements
    assert any('iteration' in str(detail).lower() for detail in error_detail)


@given('I want to create Monte Carlo parameters')
def step_want_create_monte_carlo_parameters(context: Context):
    """Initialize Monte Carlo parameter creation."""
    context.mc_param_data = {
        'parameter_name': 'test_param',
        'parameter_description': 'Test parameter for validation',
        'distribution_type': 'normal'
    }


@when('I specify a "{distribution}" distribution without required "{param}" parameter')
def step_specify_distribution_missing_param(context: Context, distribution: str, param: str):
    """Specify distribution without required parameter."""
    context.mc_param_data['distribution_type'] = distribution
    context.mc_param_data['distribution_parameters'] = {
        'mean': 100  # Missing 'std' for normal distribution
    }


@then('the system should reject the parameter configuration')
def step_system_rejects_parameter_config(context: Context):
    """Verify parameter configuration is rejected."""
    # This would be tested at the service level
    # For BDD, we simulate the validation
    from src.cso_platform.schemas.enhanced_risk import MonteCarloParameterCreate
    
    try:
        param = MonteCarloParameterCreate(**context.mc_param_data)
        # If we get here, validation didn't catch the error
        assert False, "Expected validation error but none occurred"
    except Exception as e:
        context.parameter_validation_error = str(e)


@then('I should receive a validation error about missing distribution parameters')
def step_validation_error_missing_distribution_params(context: Context):
    """Verify validation error about missing distribution parameters."""
    error_message = context.parameter_validation_error
    assert 'distribution' in error_message.lower() or 'parameter' in error_message.lower()


# Performance Steps

@given('I have a valid enhanced risk calculation setup')
def step_valid_enhanced_calculation_setup(context: Context):
    """Set up valid enhanced risk calculation for performance testing."""
    # Create a risk profile first
    profile_data = {
        'name': 'Performance Test Profile',
        'industry': 'technology',
        'organization_size': 'medium',
        'data_sensitivity_level': 3,
        'previous_incidents': 0,
        'current_security_maturity': 3,
        'business_criticality': 'medium'
    }
    
    response = requests.post(
        f"{context.base_url}/api/v1/enhanced-risk/risk-profiles",
        json=profile_data,
        headers=context.auth_headers
    )
    
    assert response.status_code == 201
    context.performance_profile = response.json()
    
    # Set up calculation data
    context.performance_calc_data = {
        'name': 'Performance Test Calculation',
        'description': 'Testing calculation performance',
        'base_calculation_id': 1,
        'risk_profile_id': context.performance_profile['id'],
        'random_seed': 42,
        'monte_carlo_parameters': [
            {
                'parameter_name': 'breach_cost',
                'distribution_type': 'lognormal',
                'distribution_parameters': {'mean': 15.4, 'std': 0.5},
                'min_value': 1000000,
                'max_value': 50000000
            }
        ]
    }


@when('I run a simulation with {iterations:d} iterations')
def step_run_simulation_with_iterations(context: Context, iterations: int):
    """Run simulation with specified iterations."""
    import time
    
    context.performance_calc_data['simulation_iterations'] = iterations
    
    start_time = time.time()
    response = requests.post(
        f"{context.base_url}/api/v1/enhanced-risk/calculations",
        json=context.performance_calc_data,
        headers=context.auth_headers
    )
    end_time = time.time()
    
    assert response.status_code == 201
    context.performance_result = response.json()
    context.actual_duration = end_time - start_time


@then('the calculation should complete within {max_seconds:d} seconds')
def step_calculation_completes_within_time(context: Context, max_seconds: int):
    """Verify calculation completes within specified time."""
    assert context.actual_duration <= max_seconds, \
        f"Calculation took {context.actual_duration:.2f}s, expected <= {max_seconds}s"


@then('the system should report the calculation duration')
def step_system_reports_calculation_duration(context: Context):
    """Verify system reports calculation duration."""
    assert 'calculation_duration_ms' in context.performance_result
    duration_ms = context.performance_result['calculation_duration_ms']
    assert duration_ms is not None
    assert duration_ms > 0


@then('convergence should be achieved within the iteration limit')
def step_convergence_achieved_within_limit(context: Context):
    """Verify convergence is achieved."""
    # This would be checked in the simulation results
    # For BDD testing, we assume convergence if calculation completed
    assert context.performance_result['simulation_iterations'] > 0


# Reporting Steps

@when('I request the calculation summary')
def step_request_calculation_summary(context: Context):
    """Request calculation summary."""
    calc_id = context.performance_result['id']
    response = requests.get(
        f"{context.base_url}/api/v1/enhanced-risk/calculations/{calc_id}/summary",
        headers=context.auth_headers
    )
    
    assert response.status_code == 200
    context.calculation_summary = response.json()


@then('I should receive a summary containing')
def step_receive_summary_containing(context: Context):
    """Verify summary contains specified metrics."""
    summary = context.calculation_summary
    
    for row in context.table:
        metric = row['metric']
        included = row['included'].lower() == 'yes'
        
        if included:
            # Map metric names to actual response fields
            field_mapping = {
                'Enhanced ROI percentage': 'enhanced_roi',
                'Risk-adjusted value': 'risk_adjusted_value',
                'Expected annual loss': 'expected_annual_loss',
                'VaR at 95%': 'var_95',
                'VaR at 99%': 'var_99',
                'Confidence intervals': 'confidence_intervals',
                'Calculation duration': 'calculation_time_ms',
                'Iteration count': 'iterations'
            }
            
            field_name = field_mapping.get(metric, metric.lower().replace(' ', '_'))
            assert field_name in summary, f"Missing {metric} in summary"


# Data Persistence Steps

@given('I have created a risk profile named "{profile_name}"')
def step_created_risk_profile_named(context: Context, profile_name: str):
    """Create a risk profile with specified name."""
    profile_data = {
        'name': profile_name,
        'industry': 'technology',
        'organization_size': 'medium',
        'data_sensitivity_level': 3,
        'previous_incidents': 0,
        'current_security_maturity': 3,
        'business_criticality': 'medium'
    }
    
    response = requests.post(
        f"{context.base_url}/api/v1/enhanced-risk/risk-profiles",
        json=profile_data,
        headers=context.auth_headers
    )
    
    assert response.status_code == 201
    context.named_profile = response.json()
    context.profile_name = profile_name


@when('I list my risk profiles')
def step_list_my_risk_profiles(context: Context):
    """List user's risk profiles."""
    response = requests.get(
        f"{context.base_url}/api/v1/enhanced-risk/risk-profiles",
        headers=context.auth_headers
    )
    
    assert response.status_code == 200
    context.profile_list = response.json()


@then('I should see "{profile_name}" in the list')
def step_see_profile_in_list(context: Context, profile_name: str):
    """Verify profile appears in the list."""
    profile_names = [profile['name'] for profile in context.profile_list]
    assert profile_name in profile_names


@then('I should be able to retrieve the full profile details')
def step_retrieve_full_profile_details(context: Context):
    """Verify full profile details can be retrieved."""
    profile_id = context.named_profile['id']
    response = requests.get(
        f"{context.base_url}/api/v1/enhanced-risk/risk-profiles/{profile_id}",
        headers=context.auth_headers
    )
    
    assert response.status_code == 200
    context.retrieved_profile = response.json()


@then('all the original parameters should be preserved')
def step_original_parameters_preserved(context: Context):
    """Verify original parameters are preserved."""
    original = context.named_profile
    retrieved = context.retrieved_profile
    
    # Check key fields are preserved
    assert original['name'] == retrieved['name']
    assert original['industry'] == retrieved['industry']
    assert original['organization_size'] == retrieved['organization_size']
    assert original['data_sensitivity_level'] == retrieved['data_sensitivity_level']


# Error Handling Steps

@when('I reference a non-existent base calculation ID "{calc_id}"')
def step_reference_nonexistent_base_calculation(context: Context, calc_id: str):
    """Reference non-existent base calculation."""
    context.invalid_calc_data = {
        'name': 'Invalid Calculation',
        'base_calculation_id': int(calc_id),
        'risk_profile_id': 1,  # Assume this exists
        'simulation_iterations': 1000,
        'monte_carlo_parameters': []
    }


@when('I reference a non-existent risk profile ID "{profile_id}"')
def step_reference_nonexistent_risk_profile(context: Context, profile_id: str):
    """Reference non-existent risk profile."""
    context.invalid_calc_data = {
        'name': 'Invalid Calculation',
        'base_calculation_id': 1,  # Assume this exists
        'risk_profile_id': int(profile_id),
        'simulation_iterations': 1000,
        'monte_carlo_parameters': []
    }


@then('the system should return a "not found" error')
def step_system_returns_not_found_error(context: Context):
    """Verify system returns not found error."""
    response = requests.post(
        f"{context.base_url}/api/v1/enhanced-risk/calculations",
        json=context.invalid_calc_data,
        headers=context.auth_headers
    )
    
    assert response.status_code == 404
    context.not_found_error = response.json()


@then('the error message should clearly indicate the missing {resource_type}')
def step_error_indicates_missing_resource(context: Context, resource_type: str):
    """Verify error message indicates missing resource."""
    error_detail = context.not_found_error['detail']
    assert resource_type.lower() in error_detail.lower()
    assert 'not found' in error_detail.lower()
