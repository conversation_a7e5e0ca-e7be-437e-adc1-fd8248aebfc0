Feature: Enhanced Risk Modeling and Monte Carlo Simulations
  As a security analyst
  I want to perform advanced risk calculations with Monte Carlo simulations
  So that I can make data-driven investment decisions with confidence intervals

  Background:
    Given I am a logged-in security analyst
    And I have access to the enhanced risk modeling features

  @risk_profile
  Scenario: Create a healthcare organization risk profile
    Given I want to create a risk profile for my organization
    When I provide the following organization details:
      | field                    | value                    |
      | name                     | Regional Healthcare Corp |
      | industry                 | healthcare               |
      | organization_size        | medium                   |
      | annual_revenue          | 50000000                 |
      | employee_count          | 1200                     |
      | data_sensitivity_level  | 5                        |
      | regulatory_requirements | HIPAA,SOC2               |
      | previous_incidents      | 2                        |
      | current_security_maturity| 3                       |
      | business_criticality    | high                     |
    Then the risk profile should be created successfully
    And the breach cost multiplier should be calculated based on healthcare industry standards
    And the breach probability should reflect the organization's security maturity

  @risk_profile
  Scenario: Validate industry-specific breach cost calculations
    Given I have a risk profile for a "healthcare" organization with "medium" size
    When the system calculates the breach cost multiplier
    Then the multiplier should be higher than the financial industry average
    And the calculation should include regulatory compliance factors
    And the result should be based on IBM Cost of Breach study data

  @risk_profile
  Scenario: Reject invalid organization size and employee count combination
    Given I want to create a risk profile
    When I specify organization size as "small" but employee count as "5000"
    Then the system should reject the profile creation
    And I should receive a validation error about mismatched organization size

  @monte_carlo
  Scenario: Perform Monte Carlo simulation for security investment
    Given I have a base ROI calculation for a security tool investment
    And I have a healthcare risk profile
    When I create an enhanced risk calculation with the following parameters:
      | parameter_name        | distribution_type | mean    | std   | min     | max      |
      | breach_cost          | lognormal         | 15.4    | 0.5   | 1000000 | 50000000 |
      | breach_probability   | beta              | 2       | 5     | 0.05    | 0.95     |
      | risk_reduction_factor| triangular        | 0.05    | 0.15  | 0.35    |          |
    And I set simulation iterations to "10000"
    Then the Monte Carlo simulation should complete successfully
    And I should receive enhanced ROI calculations
    And the results should include confidence intervals
    And Value at Risk metrics should be calculated

  @monte_carlo
  Scenario: Generate confidence intervals for investment analysis
    Given I have completed a Monte Carlo simulation with 10000 iterations
    When I request the calculation results
    Then I should receive confidence intervals at the following percentiles:
      | percentile | description |
      | 5          | Worst case  |
      | 25         | Lower quartile |
      | 50         | Median      |
      | 75         | Upper quartile |
      | 95         | Best case   |
    And each interval should have realistic values based on the input parameters

  @monte_carlo
  Scenario: Calculate Value at Risk for security investment
    Given I have completed an enhanced risk calculation
    When I analyze the Value at Risk metrics
    Then I should receive VaR at 95% confidence level
    And I should receive VaR at 99% confidence level
    And I should receive Conditional VaR (Expected Shortfall) at 95%
    And all VaR values should be within reasonable bounds

  @sensitivity_analysis
  Scenario: Perform sensitivity analysis on risk parameters
    Given I have completed a Monte Carlo simulation
    When I request sensitivity analysis results
    Then I should receive parameter sensitivity rankings
    And the most influential parameters should be identified
    And correlation coefficients should be provided for each parameter
    And tornado chart data should be available for visualization

  @business_logic
  Scenario: Healthcare organization gets appropriate risk multipliers
    Given I have a healthcare organization risk profile
    When the system calculates risk metrics
    Then the breach cost should reflect healthcare industry premiums
    And HIPAA compliance requirements should increase the multiplier
    And the breach probability should account for healthcare-specific threats

  @business_logic
  Scenario: Security maturity reduces breach probability
    Given I have two identical organization profiles
    When one has security maturity level "2" and the other has level "4"
    Then the organization with higher security maturity should have lower breach probability
    And the difference should be approximately 10% (2 levels × 5% per level)

  @business_logic
  Scenario: Previous incidents increase risk assessment
    Given I have an organization profile with "3" previous security incidents
    When the system calculates breach probability
    Then the probability should be increased by approximately 9% (3 × 3%)
    But the increase should not exceed 15% maximum

  @integration
  Scenario: Enhanced calculation integrates with base ROI calculation
    Given I have a base ROI calculation showing 150% ROI
    And I have a risk profile for a technology company
    When I create an enhanced risk calculation
    Then the enhanced ROI should incorporate risk-adjusted values
    And the result should show both optimistic and pessimistic scenarios
    And the risk-adjusted ROI should be different from the base ROI

  @validation
  Scenario: Reject Monte Carlo simulation with insufficient iterations
    Given I want to create an enhanced risk calculation
    When I specify simulation iterations as "500"
    Then the system should reject the request
    And I should receive an error about minimum iteration requirements

  @validation
  Scenario: Validate probability distribution parameters
    Given I want to create Monte Carlo parameters
    When I specify a "normal" distribution without required "std" parameter
    Then the system should reject the parameter configuration
    And I should receive a validation error about missing distribution parameters

  @performance
  Scenario: Monte Carlo simulation completes within reasonable time
    Given I have a valid enhanced risk calculation setup
    When I run a simulation with 10000 iterations
    Then the calculation should complete within 30 seconds
    And the system should report the calculation duration
    And convergence should be achieved within the iteration limit

  @reporting
  Scenario: Generate comprehensive risk calculation summary
    Given I have completed an enhanced risk calculation
    When I request the calculation summary
    Then I should receive a summary containing:
      | metric                    | included |
      | Enhanced ROI percentage   | yes      |
      | Risk-adjusted value       | yes      |
      | Expected annual loss      | yes      |
      | VaR at 95%               | yes      |
      | VaR at 99%               | yes      |
      | Confidence intervals      | yes      |
      | Calculation duration      | yes      |
      | Iteration count           | yes      |

  @data_persistence
  Scenario: Risk profiles are saved and retrievable
    Given I have created a risk profile named "Test Healthcare Profile"
    When I list my risk profiles
    Then I should see "Test Healthcare Profile" in the list
    And I should be able to retrieve the full profile details
    And all the original parameters should be preserved

  @data_persistence
  Scenario: Enhanced calculations are linked to base calculations
    Given I have a base calculation with ID "123"
    And I have created an enhanced calculation based on it
    When I retrieve the enhanced calculation
    Then it should reference base calculation ID "123"
    And I should be able to access both calculations independently

  @access_control
  Scenario: Users can only access their own risk profiles
    Given I have created a private risk profile
    And another user exists in the system
    When the other user tries to access my risk profile
    Then they should receive an access denied error
    And my profile should remain private

  @access_control
  Scenario: Public risk profiles are accessible to all users
    Given I have created a public risk profile template
    When another user lists available risk profiles
    Then they should see my public profile in their list
    And they should be able to use it as a template

  @error_handling
  Scenario: Handle missing base calculation gracefully
    Given I want to create an enhanced risk calculation
    When I reference a non-existent base calculation ID "999"
    Then the system should return a "not found" error
    And the error message should clearly indicate the missing calculation

  @error_handling
  Scenario: Handle invalid risk profile reference
    Given I want to create an enhanced risk calculation
    When I reference a non-existent risk profile ID "888"
    Then the system should return a "not found" error
    And the error message should clearly indicate the missing risk profile
