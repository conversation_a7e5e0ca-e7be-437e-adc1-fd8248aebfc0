"""Unit tests for user service.

This module contains unit tests for the UserService class,
testing user management operations and business logic.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from src.cso_platform.services.user_service import UserService
from src.cso_platform.models.user import User, UserRole
from src.cso_platform.schemas.user import UserCreate, UserUpdate, UserRegistration
from src.cso_platform.core.exceptions import (
    UserAlreadyExistsError, UserNotFoundError, InvalidCredentialsError,
    AccountLockedError
)


@pytest.fixture
def mock_db():
    """Create a mock database session."""
    db = AsyncMock()
    return db


@pytest.fixture
def user_service(mock_db):
    """Create a UserService instance with mock database."""
    return UserService(mock_db)


@pytest.fixture
def sample_user():
    """Create a sample user for testing."""
    return User(
        id=1,
        email="<EMAIL>",
        username="testuser",
        full_name="Test User",
        hashed_password="$2b$12$hashed_password",
        role=UserRole.SECURITY_ANALYST,
        organization="Test Corp",
        is_active=True,
        is_verified=True,
        failed_login_attempts="0"
    )


@pytest.fixture
def sample_user_create():
    """Create sample user creation data."""
    return UserCreate(
        email="<EMAIL>",
        username="newuser",
        full_name="New User",
        password="password123",
        role=UserRole.SECURITY_ANALYST,
        organization="Test Corp",
        terms_accepted=True,
        privacy_policy_accepted=True
    )


class TestUserService:
    """Test cases for UserService."""
    
    @pytest.mark.asyncio
    async def test_create_user_success(self, user_service, mock_db, sample_user_create):
        """Test successful user creation."""
        # Mock that no existing user is found
        mock_execute_result = AsyncMock()
        mock_execute_result.scalar_one_or_none.return_value = None
        mock_db.execute.return_value = mock_execute_result

        # Mock database operations
        mock_db.add = MagicMock()
        mock_db.commit = AsyncMock()
        mock_db.refresh = AsyncMock()

        # Create user
        result = await user_service.create_user(sample_user_create)
        
        # Verify database operations were called
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()
        
        # Verify user properties
        assert result.email == sample_user_create.email
        assert result.username == sample_user_create.username
        assert result.full_name == sample_user_create.full_name
        assert result.role == sample_user_create.role
    
    @pytest.mark.asyncio
    async def test_create_user_email_exists(self, user_service, mock_db, sample_user_create, sample_user):
        """Test user creation when email already exists."""
        # Mock that existing user is found
        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_user
        
        # Attempt to create user should raise exception
        with pytest.raises(UserAlreadyExistsError, match="User with this email already exists"):
            await user_service.create_user(sample_user_create)
    
    @pytest.mark.asyncio
    async def test_authenticate_user_success(self, user_service, mock_db, sample_user):
        """Test successful user authentication."""
        # Mock user lookup
        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_user
        mock_db.commit = AsyncMock()
        
        # Mock password verification (we'll need to patch this)
        with patch('src.cso_platform.services.user_service.verify_password', return_value=True):
            result = await user_service.authenticate_user("testuser", "password123")
        
        # Verify successful authentication
        assert result == sample_user
        assert result.failed_login_attempts == "0"
        mock_db.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_authenticate_user_wrong_password(self, user_service, mock_db, sample_user):
        """Test authentication with wrong password."""
        # Mock user lookup
        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_user
        mock_db.commit = AsyncMock()
        
        # Mock password verification failure
        with patch('src.cso_platform.services.user_service.verify_password', return_value=False):
            result = await user_service.authenticate_user("testuser", "wrongpassword")
        
        # Verify authentication failed
        assert result is None
        mock_db.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_authenticate_user_account_locked(self, user_service, mock_db, sample_user):
        """Test authentication with locked account."""
        # Set user as locked
        sample_user.failed_login_attempts = "5"
        
        # Mock user lookup
        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_user
        
        # Attempt authentication should raise exception
        with pytest.raises(AccountLockedError):
            await user_service.authenticate_user("testuser", "password123")
    
    @pytest.mark.asyncio
    async def test_authenticate_user_not_found(self, user_service, mock_db):
        """Test authentication when user doesn't exist."""
        # Mock no user found
        mock_db.execute.return_value.scalar_one_or_none.return_value = None
        
        result = await user_service.authenticate_user("nonexistent", "password123")
        
        # Verify authentication failed
        assert result is None
    
    @pytest.mark.asyncio
    async def test_get_user_by_id_success(self, user_service, mock_db, sample_user):
        """Test successful user retrieval by ID."""
        # Mock user lookup
        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_user
        
        result = await user_service.get_user_by_id(1)
        
        # Verify user returned
        assert result == sample_user
    
    @pytest.mark.asyncio
    async def test_get_user_by_id_not_found(self, user_service, mock_db):
        """Test user retrieval when user doesn't exist."""
        # Mock no user found
        mock_db.execute.return_value.scalar_one_or_none.return_value = None
        
        result = await user_service.get_user_by_id(999)
        
        # Verify no user returned
        assert result is None
    
    @pytest.mark.asyncio
    async def test_get_user_by_email_success(self, user_service, mock_db, sample_user):
        """Test successful user retrieval by email."""
        # Mock user lookup
        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_user
        
        result = await user_service.get_user_by_email("<EMAIL>")
        
        # Verify user returned
        assert result == sample_user
    
    @pytest.mark.asyncio
    async def test_update_user_success(self, user_service, mock_db, sample_user):
        """Test successful user update."""
        # Mock user lookup
        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_user
        mock_db.commit = AsyncMock()
        mock_db.refresh = AsyncMock()
        
        # Create update data
        update_data = UserUpdate(
            full_name="Updated Name",
            organization="Updated Corp"
        )
        
        result = await user_service.update_user(1, update_data)
        
        # Verify user was updated
        assert result.full_name == "Updated Name"
        assert result.organization == "Updated Corp"
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_user_not_found(self, user_service, mock_db):
        """Test user update when user doesn't exist."""
        # Mock no user found
        mock_db.execute.return_value.scalar_one_or_none.return_value = None
        
        update_data = UserUpdate(full_name="Updated Name")
        
        # Attempt update should raise exception
        with pytest.raises(UserNotFoundError):
            await user_service.update_user(999, update_data)
    
    @pytest.mark.asyncio
    async def test_change_password_success(self, user_service, mock_db, sample_user):
        """Test successful password change."""
        # Mock user lookup
        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_user
        mock_db.commit = AsyncMock()
        
        # Mock password verification and hashing
        with patch('src.cso_platform.services.user_service.verify_password', return_value=True), \
             patch('src.cso_platform.services.user_service.get_password_hash', return_value="new_hashed_password"):
            
            result = await user_service.change_password(1, "oldpassword", "newpassword")
        
        # Verify password change succeeded
        assert result is True
        assert sample_user.hashed_password == "new_hashed_password"
        assert sample_user.password_changed_at is not None
        mock_db.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_change_password_wrong_current(self, user_service, mock_db, sample_user):
        """Test password change with wrong current password."""
        # Mock user lookup
        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_user
        
        # Mock password verification failure
        with patch('src.cso_platform.services.user_service.verify_password', return_value=False):
            
            # Attempt password change should raise exception
            with pytest.raises(InvalidCredentialsError):
                await user_service.change_password(1, "wrongpassword", "newpassword")
    
    @pytest.mark.asyncio
    async def test_deactivate_user_success(self, user_service, mock_db, sample_user):
        """Test successful user deactivation."""
        # Mock user lookup
        mock_db.execute.return_value.scalar_one_or_none.return_value = sample_user
        mock_db.commit = AsyncMock()
        
        result = await user_service.deactivate_user(1)
        
        # Verify user was deactivated
        assert result is True
        assert sample_user.is_active is False
        mock_db.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_register_user_success(self, user_service, mock_db):
        """Test successful user registration."""
        # Mock that no existing user is found
        mock_db.execute.return_value.scalar_one_or_none.return_value = None
        
        # Mock database operations
        mock_db.add = MagicMock()
        mock_db.commit = AsyncMock()
        mock_db.refresh = AsyncMock()
        
        # Create registration data
        registration_data = UserRegistration(
            email="<EMAIL>",
            username="newuser",
            full_name="New User",
            password="password123",
            confirm_password="password123",
            role=UserRole.SECURITY_ANALYST,
            terms_accepted=True,
            privacy_policy_accepted=True
        )
        
        # Register user
        result = await user_service.register_user(registration_data)
        
        # Verify database operations were called
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()
        
        # Verify user properties
        assert result.email == registration_data.email
        assert result.username == registration_data.username
    
    @pytest.mark.asyncio
    async def test_register_user_password_mismatch(self, user_service, mock_db):
        """Test user registration with password mismatch."""
        # Create registration data with mismatched passwords
        registration_data = UserRegistration(
            email="<EMAIL>",
            username="newuser",
            full_name="New User",
            password="password123",
            confirm_password="different_password",
            role=UserRole.SECURITY_ANALYST,
            terms_accepted=True,
            privacy_policy_accepted=True
        )
        
        # Registration should raise exception
        with pytest.raises(ValueError, match="Passwords do not match"):
            await user_service.register_user(registration_data)
