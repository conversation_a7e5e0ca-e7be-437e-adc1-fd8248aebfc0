<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">38%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-20 16:07 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_a8e3d2a25284f10c___init___py.html">src/cso_platform/__init__.py</a></td>
                <td class="name left"><a href="z_a8e3d2a25284f10c___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7d55aa91cc0e4b64___init___py.html">src/cso_platform/api/__init__.py</a></td>
                <td class="name left"><a href="z_7d55aa91cc0e4b64___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d6d90c030a097e00___init___py.html">src/cso_platform/api/middleware/__init__.py</a></td>
                <td class="name left"><a href="z_d6d90c030a097e00___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ff330425635f025___init___py.html">src/cso_platform/api/v1/__init__.py</a></td>
                <td class="name left"><a href="z_0ff330425635f025___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ff330425635f025_dependencies_py.html#t20">src/cso_platform/api/v1/dependencies.py</a></td>
                <td class="name left"><a href="z_0ff330425635f025_dependencies_py.html#t20"><data value='get_current_user_id'>get_current_user_id</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ff330425635f025_dependencies_py.html#t47">src/cso_platform/api/v1/dependencies.py</a></td>
                <td class="name left"><a href="z_0ff330425635f025_dependencies_py.html#t47"><data value='get_current_user_id_optional'>get_current_user_id_optional</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ff330425635f025_dependencies_py.html">src/cso_platform/api/v1/dependencies.py</a></td>
                <td class="name left"><a href="z_0ff330425635f025_dependencies_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c___init___py.html">src/cso_platform/api/v1/endpoints/__init__.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_auth_py.html#t37">src/cso_platform/api/v1/endpoints/auth.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_auth_py.html#t37"><data value='get_current_user'>get_current_user</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_auth_py.html#t80">src/cso_platform/api/v1/endpoints/auth.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_auth_py.html#t80"><data value='get_current_active_user'>get_current_active_user</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_auth_py.html#t103">src/cso_platform/api/v1/endpoints/auth.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_auth_py.html#t103"><data value='register'>register</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_auth_py.html#t137">src/cso_platform/api/v1/endpoints/auth.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_auth_py.html#t137"><data value='login'>login</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_auth_py.html#t201">src/cso_platform/api/v1/endpoints/auth.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_auth_py.html#t201"><data value='refresh_token'>refresh_token</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_auth_py.html#t256">src/cso_platform/api/v1/endpoints/auth.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_auth_py.html#t256"><data value='get_current_user_info'>get_current_user_info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_auth_py.html#t271">src/cso_platform/api/v1/endpoints/auth.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_auth_py.html#t271"><data value='change_password'>change_password</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_auth_py.html#t315">src/cso_platform/api/v1/endpoints/auth.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_auth_py.html#t315"><data value='request_password_reset'>request_password_reset</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_auth_py.html#t344">src/cso_platform/api/v1/endpoints/auth.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_auth_py.html#t344"><data value='confirm_password_reset'>confirm_password_reset</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_auth_py.html">src/cso_platform/api/v1/endpoints/auth.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_auth_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html#t29">src/cso_platform/api/v1/endpoints/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html#t29"><data value='create_risk_profile'>create_risk_profile</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html#t53">src/cso_platform/api/v1/endpoints/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html#t53"><data value='list_risk_profiles'>list_risk_profiles</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html#t77">src/cso_platform/api/v1/endpoints/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html#t77"><data value='get_risk_profile'>get_risk_profile</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html#t100">src/cso_platform/api/v1/endpoints/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html#t100"><data value='update_risk_profile'>update_risk_profile</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html#t126">src/cso_platform/api/v1/endpoints/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html#t126"><data value='delete_risk_profile'>delete_risk_profile</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html#t153">src/cso_platform/api/v1/endpoints/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html#t153"><data value='create_enhanced_calculation'>create_enhanced_calculation</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html#t204">src/cso_platform/api/v1/endpoints/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html#t204"><data value='list_enhanced_calculations'>list_enhanced_calculations</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html#t234">src/cso_platform/api/v1/endpoints/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html#t234"><data value='get_enhanced_calculation'>get_enhanced_calculation</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html#t280">src/cso_platform/api/v1/endpoints/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html#t280"><data value='get_calculation_summary'>get_calculation_summary</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html#t300">src/cso_platform/api/v1/endpoints/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html#t300"><data value='delete_enhanced_calculation'>delete_enhanced_calculation</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html">src/cso_platform/api/v1/endpoints/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_roi_calculations_py.html#t34">src/cso_platform/api/v1/endpoints/roi_calculations.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_roi_calculations_py.html#t34"><data value='quick_calculate_roi'>quick_calculate_roi</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_roi_calculations_py.html#t66">src/cso_platform/api/v1/endpoints/roi_calculations.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_roi_calculations_py.html#t66"><data value='create_roi_calculation'>create_roi_calculation</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_roi_calculations_py.html#t118">src/cso_platform/api/v1/endpoints/roi_calculations.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_roi_calculations_py.html#t118"><data value='list_roi_calculations'>list_roi_calculations</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_roi_calculations_py.html#t175">src/cso_platform/api/v1/endpoints/roi_calculations.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_roi_calculations_py.html#t175"><data value='get_roi_calculation'>get_roi_calculation</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_roi_calculations_py.html#t228">src/cso_platform/api/v1/endpoints/roi_calculations.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_roi_calculations_py.html#t228"><data value='update_roi_calculation'>update_roi_calculation</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_roi_calculations_py.html#t283">src/cso_platform/api/v1/endpoints/roi_calculations.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_roi_calculations_py.html#t283"><data value='delete_roi_calculation'>delete_roi_calculation</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_roi_calculations_py.html">src/cso_platform/api/v1/endpoints/roi_calculations.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_roi_calculations_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_users_py.html#t26">src/cso_platform/api/v1/endpoints/users.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_users_py.html#t26"><data value='check_admin_or_self'>check_admin_or_self</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_users_py.html#t43">src/cso_platform/api/v1/endpoints/users.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_users_py.html#t43"><data value='check_admin_access'>check_admin_access</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_users_py.html#t59">src/cso_platform/api/v1/endpoints/users.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_users_py.html#t59"><data value='get_users'>get_users</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_users_py.html#t106">src/cso_platform/api/v1/endpoints/users.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_users_py.html#t106"><data value='get_user'>get_user</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_users_py.html#t145">src/cso_platform/api/v1/endpoints/users.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_users_py.html#t145"><data value='update_user'>update_user</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_users_py.html#t197">src/cso_platform/api/v1/endpoints/users.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_users_py.html#t197"><data value='update_user_profile'>update_user_profile</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_users_py.html#t246">src/cso_platform/api/v1/endpoints/users.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_users_py.html#t246"><data value='deactivate_user'>deactivate_user</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_users_py.html#t290">src/cso_platform/api/v1/endpoints/users.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_users_py.html#t290"><data value='activate_user'>activate_user</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_users_py.html#t333">src/cso_platform/api/v1/endpoints/users.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_users_py.html#t333"><data value='create_user'>create_user</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_users_py.html">src/cso_platform/api/v1/endpoints/users.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_users_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ff330425635f025_router_py.html#t16">src/cso_platform/api/v1/router.py</a></td>
                <td class="name left"><a href="z_0ff330425635f025_router_py.html#t16"><data value='health_check'>health_check</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ff330425635f025_router_py.html#t33">src/cso_platform/api/v1/router.py</a></td>
                <td class="name left"><a href="z_0ff330425635f025_router_py.html#t33"><data value='get_services'>get_services</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ff330425635f025_router_py.html">src/cso_platform/api/v1/router.py</a></td>
                <td class="name left"><a href="z_0ff330425635f025_router_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="2 13">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9___init___py.html">src/cso_platform/core/__init__.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_auth_py.html#t22">src/cso_platform/core/auth.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_auth_py.html#t22"><data value='get_current_user'>get_current_user</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_auth_py.html#t59">src/cso_platform/core/auth.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_auth_py.html#t59"><data value='get_current_active_user'>get_current_active_user</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_auth_py.html#t82">src/cso_platform/core/auth.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_auth_py.html#t82"><data value='get_current_superuser'>get_current_superuser</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_auth_py.html#t105">src/cso_platform/core/auth.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_auth_py.html#t105"><data value='get_optional_current_user'>get_optional_current_user</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_auth_py.html#t124">src/cso_platform/core/auth.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_auth_py.html#t124"><data value='require_user_or_admin'>require_user_or_admin</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_auth_py.html">src/cso_platform/core/auth.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_auth_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_config_py.html#t86">src/cso_platform/core/config.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_config_py.html#t86"><data value='assemble_cors_origins'>Settings.assemble_cors_origins</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_config_py.html#t95">src/cso_platform/core/config.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_config_py.html#t95"><data value='database_url'>Settings.database_url</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_config_py.html#t110">src/cso_platform/core/config.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_config_py.html#t110"><data value='test_database_url'>Settings.test_database_url</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_config_py.html#t125">src/cso_platform/core/config.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_config_py.html#t125"><data value='is_production'>Settings.is_production</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_config_py.html#t130">src/cso_platform/core/config.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_config_py.html#t130"><data value='is_development'>Settings.is_development</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_config_py.html#t135">src/cso_platform/core/config.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_config_py.html#t135"><data value='is_testing'>Settings.is_testing</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_config_py.html">src/cso_platform/core/config.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>59</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="59 59">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_database_py.html#t30">src/cso_platform/core/database.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_database_py.html#t30"><data value='get_db'>get_db</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_database_py.html#t46">src/cso_platform/core/database.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_database_py.html#t46"><data value='create_tables'>create_tables</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_database_py.html#t54">src/cso_platform/core/database.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_database_py.html#t54"><data value='drop_tables'>drop_tables</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_database_py.html">src/cso_platform/core/database.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_database_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_database_manager_py.html">src/cso_platform/core/database_manager.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_database_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t13">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t13"><data value='init__'>CSOPlatformException.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t35">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t35"><data value='init__'>ValidationError.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t50">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t50"><data value='init__'>NotFoundError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t63">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t63"><data value='init__'>AuthenticationError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t76">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t76"><data value='init__'>AuthorizationError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t89">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t89"><data value='init__'>UserAlreadyExistsError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t102">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t102"><data value='init__'>UserNotFoundError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t115">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t115"><data value='init__'>InvalidCredentialsError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t128">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t128"><data value='init__'>AccountLockedError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t141">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t141"><data value='init__'>EmailNotVerifiedError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t154">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t154"><data value='init__'>CalculationNotFoundError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t167">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t167"><data value='init__'>CalculationAccessDeniedError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t180">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t180"><data value='init__'>TemplateNotFoundError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t193">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t193"><data value='init__'>DatabaseError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t206">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t206"><data value='init__'>ExternalServiceError.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t221">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t221"><data value='init__'>RateLimitExceededError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t234">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t234"><data value='init__'>FileUploadError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t247">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t247"><data value='init__'>ConfigurationError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_security_py.html#t19">src/cso_platform/core/security.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_security_py.html#t19"><data value='create_access_token'>create_access_token</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_security_py.html#t48">src/cso_platform/core/security.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_security_py.html#t48"><data value='create_refresh_token'>create_refresh_token</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_security_py.html#t77">src/cso_platform/core/security.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_security_py.html#t77"><data value='verify_token'>verify_token</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_security_py.html#t100">src/cso_platform/core/security.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_security_py.html#t100"><data value='verify_password'>verify_password</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_security_py.html#t113">src/cso_platform/core/security.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_security_py.html#t113"><data value='get_password_hash'>get_password_hash</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_security_py.html#t125">src/cso_platform/core/security.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_security_py.html#t125"><data value='generate_password_reset_token'>generate_password_reset_token</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_security_py.html#t146">src/cso_platform/core/security.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_security_py.html#t146"><data value='verify_password_reset_token'>verify_password_reset_token</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_security_py.html">src/cso_platform/core/security.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_security_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="5 13">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a8e3d2a25284f10c_main_py.html#t43">src/cso_platform/main.py</a></td>
                <td class="name left"><a href="z_a8e3d2a25284f10c_main_py.html#t43"><data value='lifespan'>lifespan</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a8e3d2a25284f10c_main_py.html#t95">src/cso_platform/main.py</a></td>
                <td class="name left"><a href="z_a8e3d2a25284f10c_main_py.html#t95"><data value='root'>root</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a8e3d2a25284f10c_main_py.html">src/cso_platform/main.py</a></td>
                <td class="name left"><a href="z_a8e3d2a25284f10c_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="7 21">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658___init___py.html">src/cso_platform/models/__init__.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t33">src/cso_platform/models/base.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t33"><data value='created_at'>TimestampMixin.created_at</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t43">src/cso_platform/models/base.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t43"><data value='updated_at'>TimestampMixin.updated_at</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t66">src/cso_platform/models/base.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t66"><data value='deleted_at'>SoftDeleteMixin.deleted_at</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t75">src/cso_platform/models/base.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t75"><data value='is_deleted'>SoftDeleteMixin.is_deleted</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t86">src/cso_platform/models/base.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t86"><data value='soft_delete'>SoftDeleteMixin.soft_delete</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t91">src/cso_platform/models/base.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t91"><data value='restore'>SoftDeleteMixin.restore</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t97">src/cso_platform/models/base.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t97"><data value='filter_active'>SoftDeleteMixin.filter_active</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t129">src/cso_platform/models/base.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t129"><data value='repr__'>BaseModel.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html">src/cso_platform/models/base.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t201">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t201"><data value='repr__'>Calculation.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t205">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t205"><data value='mark_completed'>Calculation.mark_completed</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t210">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t210"><data value='create_revision'>Calculation.create_revision</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t223">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t223"><data value='share_with_email'>Calculation.share_with_email</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t230">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t230"><data value='unshare_with_email'>Calculation.unshare_with_email</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t235">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t235"><data value='is_shared_with'>Calculation.is_shared_with</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t239">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t239"><data value='get_summary'>Calculation.get_summary</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t324">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t324"><data value='repr__'>CalculationTemplate.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t328">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t328"><data value='increment_usage'>CalculationTemplate.increment_usage</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t441">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t441"><data value='repr__'>RiskProfile.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t445">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t445"><data value='get_breach_cost_multiplier'>RiskProfile.get_breach_cost_multiplier</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t479">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t479"><data value='get_breach_probability'>RiskProfile.get_breach_probability</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t640">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t640"><data value='repr__'>EnhancedRiskCalculation.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t644">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t644"><data value='get_summary_statistics'>EnhancedRiskCalculation.get_summary_statistics</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t661">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t661"><data value='get_risk_metrics'>EnhancedRiskCalculation.get_risk_metrics</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t748">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t748"><data value='repr__'>MonteCarloParameter.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t752">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t752"><data value='validate_distribution_parameters'>MonteCarloParameter.validate_distribution_parameters</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t773">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t773"><data value='get_distribution_summary'>MonteCarloParameter.get_distribution_summary</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>160</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="160 160">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t12">src/cso_platform/models/mixins.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t12"><data value='slug'>SlugMixin.slug</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t27">src/cso_platform/models/mixins.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t27"><data value='description'>DescriptionMixin.description</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t40">src/cso_platform/models/mixins.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t40"><data value='name'>NameMixin.name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t53">src/cso_platform/models/mixins.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t53"><data value='title'>TitleMixin.title</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t66">src/cso_platform/models/mixins.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t66"><data value='status'>StatusMixin.status</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t81">src/cso_platform/models/mixins.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t81"><data value='order'>OrderMixin.order</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html">src/cso_platform/models/mixins.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_report_py.html">src/cso_platform/models/report.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_report_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>83</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="83 83">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_roi_calculation_py.html#t148">src/cso_platform/models/roi_calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_roi_calculation_py.html#t148"><data value='repr__'>ROICalculation.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_roi_calculation_py.html#t156">src/cso_platform/models/roi_calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_roi_calculation_py.html#t156"><data value='input_summary'>ROICalculation.input_summary</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_roi_calculation_py.html#t167">src/cso_platform/models/roi_calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_roi_calculation_py.html#t167"><data value='results_summary'>ROICalculation.results_summary</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_roi_calculation_py.html">src/cso_platform/models/roi_calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_roi_calculation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html#t191">src/cso_platform/models/user.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html#t191"><data value='repr__'>User.__repr__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html#t195">src/cso_platform/models/user.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html#t195"><data value='update_last_login'>User.update_last_login</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html#t200">src/cso_platform/models/user.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html#t200"><data value='increment_failed_login'>User.increment_failed_login</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html#t205">src/cso_platform/models/user.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html#t205"><data value='reset_failed_login_attempts'>User.reset_failed_login_attempts</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html#t209">src/cso_platform/models/user.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html#t209"><data value='is_account_locked'>User.is_account_locked</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html#t214">src/cso_platform/models/user.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html#t214"><data value='accept_terms'>User.accept_terms</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html#t218">src/cso_platform/models/user.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html#t218"><data value='accept_privacy_policy'>User.accept_privacy_policy</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html#t222">src/cso_platform/models/user.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html#t222"><data value='has_role'>User.has_role</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html">src/cso_platform/models/user.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>53</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="53 53">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026___init___py.html">src/cso_platform/schemas/__init__.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_base_py.html#t94">src/cso_platform/schemas/base.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_base_py.html#t94"><data value='calculate_pages'>PaginatedResponse.calculate_pages</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_base_py.html">src/cso_platform/schemas/base.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html">src/cso_platform/schemas/calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>93</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="93 93">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t68">src/cso_platform/schemas/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t68"><data value='validate_regulatory_requirements'>RiskProfileCreate.validate_regulatory_requirements</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t81">src/cso_platform/schemas/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t81"><data value='validate_geographic_regions'>RiskProfileCreate.validate_geographic_regions</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t143">src/cso_platform/schemas/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t143"><data value='calculate_risk_metrics'>RiskProfileResponse.calculate_risk_metrics</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t162">src/cso_platform/schemas/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t162"><data value='validate_distribution_type'>MonteCarloParameterCreate.validate_distribution_type</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t170">src/cso_platform/schemas/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t170"><data value='validate_distribution_parameters'>MonteCarloParameterCreate.validate_distribution_parameters</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html">src/cso_platform/schemas/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>168</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="168 168">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t36">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t36"><data value='validate_template_config'>ReportTemplateCreate.validate_template_config</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t82">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t82"><data value='validate_generation_params'>ReportCreate.validate_generation_params</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t168">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t168"><data value='validate_hex_color'>BrandConfigurationCreate.validate_hex_color</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>112</td>
                <td>112</td>
                <td>0</td>
                <td class="right" data-ratio="0 112">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html#t69">src/cso_platform/schemas/roi_calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html#t69"><data value='validate_currency_amounts'>ROICalculationBase.validate_currency_amounts</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html#t77">src/cso_platform/schemas/roi_calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html#t77"><data value='validate_hours_per_test'>ROICalculationBase.validate_hours_per_test</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html">src/cso_platform/schemas/roi_calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>53</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="53 53">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t97">src/cso_platform/schemas/user.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t97"><data value='validate_passwords_match'>UserRegistration.validate_passwords_match</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t126">src/cso_platform/schemas/user.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t126"><data value='validate_passwords_match'>PasswordChange.validate_passwords_match</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t146">src/cso_platform/schemas/user.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t146"><data value='validate_passwords_match'>PasswordResetConfirm.validate_passwords_match</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html">src/cso_platform/schemas/user.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>83</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="83 83">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70___init___py.html">src/cso_platform/services/__init__.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t35">src/cso_platform/services/enhanced_risk_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t35"><data value='init__'>EnhancedRiskService.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t41">src/cso_platform/services/enhanced_risk_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t41"><data value='create_risk_profile'>EnhancedRiskService.create_risk_profile</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t77">src/cso_platform/services/enhanced_risk_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t77"><data value='get_risk_profile'>EnhancedRiskService.get_risk_profile</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t89">src/cso_platform/services/enhanced_risk_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t89"><data value='update_risk_profile'>EnhancedRiskService.update_risk_profile</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t123">src/cso_platform/services/enhanced_risk_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t123"><data value='list_risk_profiles'>EnhancedRiskService.list_risk_profiles</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t138">src/cso_platform/services/enhanced_risk_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t138"><data value='create_enhanced_calculation'>EnhancedRiskService.create_enhanced_calculation</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t218">src/cso_platform/services/enhanced_risk_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t218"><data value='get_enhanced_calculation'>EnhancedRiskService.get_enhanced_calculation</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t230">src/cso_platform/services/enhanced_risk_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t230"><data value='list_enhanced_calculations'>EnhancedRiskService.list_enhanced_calculations</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t238">src/cso_platform/services/enhanced_risk_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t238"><data value='validate_risk_profile_data'>EnhancedRiskService._validate_risk_profile_data</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t273">src/cso_platform/services/enhanced_risk_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t273"><data value='run_monte_carlo_simulation'>EnhancedRiskService._run_monte_carlo_simulation</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t344">src/cso_platform/services/enhanced_risk_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t344"><data value='setup_monte_carlo_parameters'>EnhancedRiskService._setup_monte_carlo_parameters</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t400">src/cso_platform/services/enhanced_risk_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t400"><data value='calculate_cost_intervals'>EnhancedRiskService._calculate_cost_intervals</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t412">src/cso_platform/services/enhanced_risk_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t412"><data value='calculate_benefit_intervals'>EnhancedRiskService._calculate_benefit_intervals</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html">src/cso_platform/services/enhanced_risk_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>27</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="12 27">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_roi_calculation_py.html#t24">src/cso_platform/services/roi_calculation.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_roi_calculation_py.html#t24"><data value='init__'>ROICalculationService.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_roi_calculation_py.html#t32">src/cso_platform/services/roi_calculation.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_roi_calculation_py.html#t32"><data value='calculate_basic_roi'>ROICalculationService.calculate_basic_roi</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_roi_calculation_py.html#t98">src/cso_platform/services/roi_calculation.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_roi_calculation_py.html#t98"><data value='quick_calculate'>ROICalculationService.quick_calculate</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_roi_calculation_py.html#t115">src/cso_platform/services/roi_calculation.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_roi_calculation_py.html#t115"><data value='create_calculation'>ROICalculationService.create_calculation</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_roi_calculation_py.html#t164">src/cso_platform/services/roi_calculation.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_roi_calculation_py.html#t164"><data value='get_calculation'>ROICalculationService.get_calculation</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_roi_calculation_py.html#t178">src/cso_platform/services/roi_calculation.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_roi_calculation_py.html#t178"><data value='list_calculations'>ROICalculationService.list_calculations</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_roi_calculation_py.html#t204">src/cso_platform/services/roi_calculation.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_roi_calculation_py.html#t204"><data value='update_calculation'>ROICalculationService.update_calculation</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_roi_calculation_py.html#t261">src/cso_platform/services/roi_calculation.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_roi_calculation_py.html#t261"><data value='delete_calculation'>ROICalculationService.delete_calculation</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_roi_calculation_py.html">src/cso_platform/services/roi_calculation.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_roi_calculation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t21">src/cso_platform/services/user.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t21"><data value='init__'>UserService.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t29">src/cso_platform/services/user.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t29"><data value='create_user'>UserService.create_user</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t76">src/cso_platform/services/user.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t76"><data value='authenticate_user'>UserService.authenticate_user</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t102">src/cso_platform/services/user.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t102"><data value='get_user'>UserService.get_user</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t119">src/cso_platform/services/user.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t119"><data value='get_user_by_email'>UserService.get_user_by_email</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t136">src/cso_platform/services/user.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t136"><data value='get_user_by_username'>UserService.get_user_by_username</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t153">src/cso_platform/services/user.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t153"><data value='update_user'>UserService.update_user</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t199">src/cso_platform/services/user.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t199"><data value='deactivate_user'>UserService.deactivate_user</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t217">src/cso_platform/services/user.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t217"><data value='delete_user'>UserService.delete_user</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t235">src/cso_platform/services/user.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t235"><data value='list_users'>UserService.list_users</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t261">src/cso_platform/services/user.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t261"><data value='get_user_stats'>UserService.get_user_stats</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html">src/cso_platform/services/user.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t26">src/cso_platform/services/user_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t26"><data value='init__'>UserService.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t34">src/cso_platform/services/user_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t34"><data value='create_user'>UserService.create_user</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t86">src/cso_platform/services/user_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t86"><data value='register_user'>UserService.register_user</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t121">src/cso_platform/services/user_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t121"><data value='authenticate_user'>UserService.authenticate_user</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t165">src/cso_platform/services/user_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t165"><data value='get_user_by_id'>UserService.get_user_by_id</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t179">src/cso_platform/services/user_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t179"><data value='get_user_by_email'>UserService.get_user_by_email</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t193">src/cso_platform/services/user_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t193"><data value='get_user_by_username'>UserService.get_user_by_username</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t207">src/cso_platform/services/user_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t207"><data value='get_user_by_email_or_username'>UserService.get_user_by_email_or_username</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t224">src/cso_platform/services/user_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t224"><data value='update_user'>UserService.update_user</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t263">src/cso_platform/services/user_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t263"><data value='change_password'>UserService.change_password</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t293">src/cso_platform/services/user_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t293"><data value='deactivate_user'>UserService.deactivate_user</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t313">src/cso_platform/services/user_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t313"><data value='get_users_by_role'>UserService.get_users_by_role</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t327">src/cso_platform/services/user_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t327"><data value='get_users_by_organization'>UserService.get_users_by_organization</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html">src/cso_platform/services/user_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="8 23">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971___init___py.html">src/cso_platform/utils/__init__.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="1 2">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t29">src/cso_platform/utils/monte_carlo.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t29"><data value='init__'>MonteCarloEngine.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t35">src/cso_platform/utils/monte_carlo.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t35"><data value='run_simulation'>MonteCarloEngine.run_simulation</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t85">src/cso_platform/utils/monte_carlo.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t85"><data value='generate_samples'>MonteCarloEngine._generate_samples</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t138">src/cso_platform/utils/monte_carlo.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t138"><data value='calculate_statistics'>MonteCarloEngine._calculate_statistics</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t161">src/cso_platform/utils/monte_carlo.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t161"><data value='sensitivity_analysis'>MonteCarloEngine._sensitivity_analysis</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t225">src/cso_platform/utils/monte_carlo.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t225"><data value='check_convergence'>MonteCarloEngine._check_convergence</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t244">src/cso_platform/utils/monte_carlo.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t244"><data value='calculate_var'>MonteCarloEngine.calculate_var</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t267">src/cso_platform/utils/monte_carlo.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t267"><data value='calculate_confidence_intervals'>MonteCarloEngine.calculate_confidence_intervals</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t289">src/cso_platform/utils/monte_carlo.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t289"><data value='init__'>RiskCalculationFunction.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t294">src/cso_platform/utils/monte_carlo.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t294"><data value='call__'>RiskCalculationFunction.__call__</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html">src/cso_platform/utils/monte_carlo.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t40">src/cso_platform/utils/service_url_manager.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t40"><data value='init__'>ServiceURLManager.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t58">src/cso_platform/utils/service_url_manager.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t58"><data value='detect_environment'>ServiceURLManager._detect_environment</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t68">src/cso_platform/utils/service_url_manager.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t68"><data value='default_config_path'>ServiceURLManager._default_config_path</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t73">src/cso_platform/utils/service_url_manager.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t73"><data value='load_configuration'>ServiceURLManager._load_configuration</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t92">src/cso_platform/utils/service_url_manager.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t92"><data value='validate_environment'>ServiceURLManager._validate_environment</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t105">src/cso_platform/utils/service_url_manager.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t105"><data value='get_service_url'>ServiceURLManager.get_service_url</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t137">src/cso_platform/utils/service_url_manager.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t137"><data value='build_service_base_url'>ServiceURLManager._build_service_base_url</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t172">src/cso_platform/utils/service_url_manager.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t172"><data value='get_all_service_urls'>ServiceURLManager.get_all_service_urls</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t187">src/cso_platform/utils/service_url_manager.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t187"><data value='health_check_urls'>ServiceURLManager.health_check_urls</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t195">src/cso_platform/utils/service_url_manager.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t195"><data value='get_api_endpoint'>ServiceURLManager.get_api_endpoint</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t237">src/cso_platform/utils/service_url_manager.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t237"><data value='switch_environment'>ServiceURLManager.switch_environment</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t255">src/cso_platform/utils/service_url_manager.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t255"><data value='list_environments'>ServiceURLManager.list_environments</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t263">src/cso_platform/utils/service_url_manager.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t263"><data value='list_services'>ServiceURLManager.list_services</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t273">src/cso_platform/utils/service_url_manager.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t273"><data value='get_environment_info'>ServiceURLManager.get_environment_info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t286">src/cso_platform/utils/service_url_manager.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t286"><data value='get_url_manager'>get_url_manager</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t303">src/cso_platform/utils/service_url_manager.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t303"><data value='get_service_url'>get_service_url</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t317">src/cso_platform/utils/service_url_manager.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t317"><data value='get_api_endpoint'>get_api_endpoint</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html">src/cso_platform/utils/service_url_manager.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="6 25">24%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>2476</td>
                <td>1527</td>
                <td>0</td>
                <td class="right" data-ratio="949 2476">38%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-20 16:07 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
