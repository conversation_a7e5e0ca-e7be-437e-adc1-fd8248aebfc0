{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.6.1", "globals": "151a65760b96cf34073fa2a5213c7e90", "files": {"z_a8e3d2a25284f10c___init___py": {"hash": "84fd1cbc3ceac089206b2cae1c9add8c", "index": {"url": "z_a8e3d2a25284f10c___init___py.html", "file": "src/cso_platform/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_7d55aa91cc0e4b64___init___py": {"hash": "ea817d53f8b4f4db4979af3e75beab97", "index": {"url": "z_7d55aa91cc0e4b64___init___py.html", "file": "src/cso_platform/api/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d6d90c030a097e00___init___py": {"hash": "1a3907a9db265509d9e3c725f6efafbb", "index": {"url": "z_d6d90c030a097e00___init___py.html", "file": "src/cso_platform/api/middleware/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0ff330425635f025___init___py": {"hash": "e73b5302b171b5ffd6692446a0fa7267", "index": {"url": "z_0ff330425635f025___init___py.html", "file": "src/cso_platform/api/v1/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0ff330425635f025_dependencies_py": {"hash": "43f3e6d3b19a881120bd580b46c8d510", "index": {"url": "z_0ff330425635f025_dependencies_py.html", "file": "src/cso_platform/api/v1/dependencies.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 19, "n_excluded": 0, "n_missing": 19, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0c218527f5a391c___init___py": {"hash": "52fe72e8ccfd189ab08bf283ba6a1bae", "index": {"url": "z_f0c218527f5a391c___init___py.html", "file": "src/cso_platform/api/v1/endpoints/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0c218527f5a391c_auth_py": {"hash": "ce7c3697d1df66d21058431d673b5060", "index": {"url": "z_f0c218527f5a391c_auth_py.html", "file": "src/cso_platform/api/v1/endpoints/auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 112, "n_excluded": 0, "n_missing": 112, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0c218527f5a391c_enhanced_risk_py": {"hash": "745fbd149a4740a577a2225cc8d99c90", "index": {"url": "z_f0c218527f5a391c_enhanced_risk_py.html", "file": "src/cso_platform/api/v1/endpoints/enhanced_risk.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 163, "n_excluded": 0, "n_missing": 163, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0c218527f5a391c_roi_calculations_py": {"hash": "8e933cc213f1f8c754f098f9354fd879", "index": {"url": "z_f0c218527f5a391c_roi_calculations_py.html", "file": "src/cso_platform/api/v1/endpoints/roi_calculations.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 44, "n_excluded": 0, "n_missing": 44, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_f0c218527f5a391c_users_py": {"hash": "b287d4c7264e0f3fc47582fb3d296f06", "index": {"url": "z_f0c218527f5a391c_users_py.html", "file": "src/cso_platform/api/v1/endpoints/users.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 103, "n_excluded": 0, "n_missing": 103, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0ff330425635f025_router_py": {"hash": "584e070acfa50348fe067b0d8acb1bc2", "index": {"url": "z_0ff330425635f025_router_py.html", "file": "src/cso_platform/api/v1/router.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 24, "n_excluded": 0, "n_missing": 22, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e3e910735ada1cf9___init___py": {"hash": "211b6fc100578c98888b3bcf950a9919", "index": {"url": "z_e3e910735ada1cf9___init___py.html", "file": "src/cso_platform/core/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e3e910735ada1cf9_auth_py": {"hash": "7481ae96cf78851a201f086234b0fa51", "index": {"url": "z_e3e910735ada1cf9_auth_py.html", "file": "src/cso_platform/core/auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 35, "n_excluded": 0, "n_missing": 35, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e3e910735ada1cf9_config_py": {"hash": "bfdbfc295b394d3456d3a5f66b421d83", "index": {"url": "z_e3e910735ada1cf9_config_py.html", "file": "src/cso_platform/core/config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 77, "n_excluded": 0, "n_missing": 18, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e3e910735ada1cf9_database_py": {"hash": "f37ab00de1c1cfafb49429dfb5e00e55", "index": {"url": "z_e3e910735ada1cf9_database_py.html", "file": "src/cso_platform/core/database.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 22, "n_excluded": 0, "n_missing": 22, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e3e910735ada1cf9_database_manager_py": {"hash": "5fcadd46e246cb350713f0013a1afbd5", "index": {"url": "z_e3e910735ada1cf9_database_manager_py.html", "file": "src/cso_platform/core/database_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e3e910735ada1cf9_exceptions_py": {"hash": "1563e79cb3d5dff4cc5cdd8fa8ab0138", "index": {"url": "z_e3e910735ada1cf9_exceptions_py.html", "file": "src/cso_platform/core/exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 60, "n_excluded": 0, "n_missing": 23, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e3e910735ada1cf9_security_py": {"hash": "eee5a3f0029308796991d3ec3361e36b", "index": {"url": "z_e3e910735ada1cf9_security_py.html", "file": "src/cso_platform/core/security.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 48, "n_excluded": 0, "n_missing": 43, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a8e3d2a25284f10c_main_py": {"hash": "fb599052fb69067b321abc9ed3632325", "index": {"url": "z_a8e3d2a25284f10c_main_py.html", "file": "src/cso_platform/main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 28, "n_excluded": 0, "n_missing": 21, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_655d59a19daa5658___init___py": {"hash": "2aafda0932602bc859107238ca9656e0", "index": {"url": "z_655d59a19daa5658___init___py.html", "file": "src/cso_platform/models/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_655d59a19daa5658_base_py": {"hash": "07cb04b7b62a827c285990aec3e49899", "index": {"url": "z_655d59a19daa5658_base_py.html", "file": "src/cso_platform/models/base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 39, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_655d59a19daa5658_calculation_py": {"hash": "8d98e5f6ee574bb03239e01842bfac8a", "index": {"url": "z_655d59a19daa5658_calculation_py.html", "file": "src/cso_platform/models/calculation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 204, "n_excluded": 0, "n_missing": 44, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_655d59a19daa5658_mixins_py": {"hash": "743576938ac7739f551634816aaff346", "index": {"url": "z_655d59a19daa5658_mixins_py.html", "file": "src/cso_platform/models/mixins.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 27, "n_excluded": 0, "n_missing": 27, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_655d59a19daa5658_report_py": {"hash": "965ececd5dd8b2aea0e41439c091c78f", "index": {"url": "z_655d59a19daa5658_report_py.html", "file": "src/cso_platform/models/report.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 83, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_655d59a19daa5658_roi_calculation_py": {"hash": "52bf48b3d876dc3a0ddd2f00aa026b4c", "index": {"url": "z_655d59a19daa5658_roi_calculation_py.html", "file": "src/cso_platform/models/roi_calculation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 31, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_655d59a19daa5658_user_py": {"hash": "778c084896cc613f0c83f47148d35d53", "index": {"url": "z_655d59a19daa5658_user_py.html", "file": "src/cso_platform/models/user.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 65, "n_excluded": 0, "n_missing": 12, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e8cb0c54856c2026___init___py": {"hash": "4e1136f0912479bbaf8f2af75ef668ea", "index": {"url": "z_e8cb0c54856c2026___init___py.html", "file": "src/cso_platform/schemas/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e8cb0c54856c2026_base_py": {"hash": "9351147b9d8f53e308aeb7d3df887162", "index": {"url": "z_e8cb0c54856c2026_base_py.html", "file": "src/cso_platform/schemas/base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 34, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e8cb0c54856c2026_calculation_py": {"hash": "2af5da8ed9298f6d50a149910207f624", "index": {"url": "z_e8cb0c54856c2026_calculation_py.html", "file": "src/cso_platform/schemas/calculation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 93, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e8cb0c54856c2026_enhanced_risk_py": {"hash": "09a80c9538bdb967a7df3ec7481cd618", "index": {"url": "z_e8cb0c54856c2026_enhanced_risk_py.html", "file": "src/cso_platform/schemas/enhanced_risk.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 195, "n_excluded": 0, "n_missing": 27, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e8cb0c54856c2026_report_py": {"hash": "fb21d7db461d8476eb9aacbbef84c706", "index": {"url": "z_e8cb0c54856c2026_report_py.html", "file": "src/cso_platform/schemas/report.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 129, "n_excluded": 0, "n_missing": 129, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e8cb0c54856c2026_roi_calculation_py": {"hash": "8f48ccef1bb3aaf28fa65cee976d0c77", "index": {"url": "z_e8cb0c54856c2026_roi_calculation_py.html", "file": "src/cso_platform/schemas/roi_calculation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 59, "n_excluded": 0, "n_missing": 6, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e8cb0c54856c2026_user_py": {"hash": "2fbe8adf75fbb4981f50eb832794b573", "index": {"url": "z_e8cb0c54856c2026_user_py.html", "file": "src/cso_platform/schemas/user.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 92, "n_excluded": 0, "n_missing": 9, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_49c5e7e7acd43c70___init___py": {"hash": "8638cf84b003ff5e423c1fad134270ab", "index": {"url": "z_49c5e7e7acd43c70___init___py.html", "file": "src/cso_platform/services/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_49c5e7e7acd43c70_enhanced_risk_service_py": {"hash": "37dae2ea399ce6209b060122bd0d14cf", "index": {"url": "z_49c5e7e7acd43c70_enhanced_risk_service_py.html", "file": "src/cso_platform/services/enhanced_risk_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 148, "n_excluded": 0, "n_missing": 136, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_49c5e7e7acd43c70_roi_calculation_py": {"hash": "31a0c50d8f4b3cecf4943c2f882a07cf", "index": {"url": "z_49c5e7e7acd43c70_roi_calculation_py.html", "file": "src/cso_platform/services/roi_calculation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 71, "n_excluded": 0, "n_missing": 56, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_49c5e7e7acd43c70_user_py": {"hash": "0019c6abcda3051fffb87b2d01478743", "index": {"url": "z_49c5e7e7acd43c70_user_py.html", "file": "src/cso_platform/services/user.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 89, "n_excluded": 0, "n_missing": 89, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_49c5e7e7acd43c70_user_service_py": {"hash": "7f176edca28bb0529a50fd3203c545b6", "index": {"url": "z_49c5e7e7acd43c70_user_service_py.html", "file": "src/cso_platform/services/user_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 100, "n_excluded": 0, "n_missing": 92, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cf6ca5f0603ea971___init___py": {"hash": "e999dade3e76d132d3814f34a0d82268", "index": {"url": "z_cf6ca5f0603ea971___init___py.html", "file": "src/cso_platform/utils/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cf6ca5f0603ea971_monte_carlo_py": {"hash": "32965061f4e73202eb51c066b02d3c59", "index": {"url": "z_cf6ca5f0603ea971_monte_carlo_py.html", "file": "src/cso_platform/utils/monte_carlo.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 164, "n_excluded": 0, "n_missing": 164, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cf6ca5f0603ea971_service_url_manager_py": {"hash": "61f0c8a8b070419bdd13750032d7e3c7", "index": {"url": "z_cf6ca5f0603ea971_service_url_manager_py.html", "file": "src/cso_platform/utils/service_url_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 102, "n_excluded": 0, "n_missing": 96, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}