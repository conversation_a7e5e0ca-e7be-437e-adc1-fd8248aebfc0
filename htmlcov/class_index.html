<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">38%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-20 16:07 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_a8e3d2a25284f10c___init___py.html">src/cso_platform/__init__.py</a></td>
                <td class="name left"><a href="z_a8e3d2a25284f10c___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7d55aa91cc0e4b64___init___py.html">src/cso_platform/api/__init__.py</a></td>
                <td class="name left"><a href="z_7d55aa91cc0e4b64___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d6d90c030a097e00___init___py.html">src/cso_platform/api/middleware/__init__.py</a></td>
                <td class="name left"><a href="z_d6d90c030a097e00___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ff330425635f025___init___py.html">src/cso_platform/api/v1/__init__.py</a></td>
                <td class="name left"><a href="z_0ff330425635f025___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ff330425635f025_dependencies_py.html">src/cso_platform/api/v1/dependencies.py</a></td>
                <td class="name left"><a href="z_0ff330425635f025_dependencies_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c___init___py.html">src/cso_platform/api/v1/endpoints/__init__.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_auth_py.html">src/cso_platform/api/v1/endpoints/auth.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_auth_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>112</td>
                <td>112</td>
                <td>0</td>
                <td class="right" data-ratio="0 112">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html">src/cso_platform/api/v1/endpoints/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>163</td>
                <td>163</td>
                <td>0</td>
                <td class="right" data-ratio="0 163">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_roi_calculations_py.html">src/cso_platform/api/v1/endpoints/roi_calculations.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_roi_calculations_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_users_py.html">src/cso_platform/api/v1/endpoints/users.py</a></td>
                <td class="name left"><a href="z_f0c218527f5a391c_users_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>103</td>
                <td>103</td>
                <td>0</td>
                <td class="right" data-ratio="0 103">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ff330425635f025_router_py.html">src/cso_platform/api/v1/router.py</a></td>
                <td class="name left"><a href="z_0ff330425635f025_router_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="2 24">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9___init___py.html">src/cso_platform/core/__init__.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_auth_py.html">src/cso_platform/core/auth.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_auth_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_config_py.html#t14">src/cso_platform/core/config.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_config_py.html#t14"><data value='Settings'>Settings</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_config_py.html">src/cso_platform/core/config.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>59</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="59 59">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_database_py.html">src/cso_platform/core/database.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_database_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_database_manager_py.html">src/cso_platform/core/database_manager.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_database_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t10">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t10"><data value='CSOPlatformException'>CSOPlatformException</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t32">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t32"><data value='ValidationError'>ValidationError</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t47">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t47"><data value='NotFoundError'>NotFoundError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t60">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t60"><data value='AuthenticationError'>AuthenticationError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t73">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t73"><data value='AuthorizationError'>AuthorizationError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t86">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t86"><data value='UserAlreadyExistsError'>UserAlreadyExistsError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t99">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t99"><data value='UserNotFoundError'>UserNotFoundError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t112">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t112"><data value='InvalidCredentialsError'>InvalidCredentialsError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t125">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t125"><data value='AccountLockedError'>AccountLockedError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t138">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t138"><data value='EmailNotVerifiedError'>EmailNotVerifiedError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t151">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t151"><data value='CalculationNotFoundError'>CalculationNotFoundError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t164">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t164"><data value='CalculationAccessDeniedError'>CalculationAccessDeniedError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t177">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t177"><data value='TemplateNotFoundError'>TemplateNotFoundError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t190">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t190"><data value='DatabaseError'>DatabaseError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t203">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t203"><data value='ExternalServiceError'>ExternalServiceError</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t218">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t218"><data value='RateLimitExceededError'>RateLimitExceededError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t231">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t231"><data value='FileUploadError'>FileUploadError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t244">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html#t244"><data value='ConfigurationError'>ConfigurationError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html">src/cso_platform/core/exceptions.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_security_py.html">src/cso_platform/core/security.py</a></td>
                <td class="name left"><a href="z_e3e910735ada1cf9_security_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>48</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="5 48">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a8e3d2a25284f10c_main_py.html">src/cso_platform/main.py</a></td>
                <td class="name left"><a href="z_a8e3d2a25284f10c_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="7 28">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658___init___py.html">src/cso_platform/models/__init__.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t18">src/cso_platform/models/base.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t18"><data value='Base'>Base</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t24">src/cso_platform/models/base.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t24"><data value='TimestampMixin'>TimestampMixin</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t54">src/cso_platform/models/base.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t54"><data value='SoftDeleteMixin'>SoftDeleteMixin</data></a></td>
                <td>7</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="2 7">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t109">src/cso_platform/models/base.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html#t109"><data value='BaseModel'>BaseModel</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html">src/cso_platform/models/base.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t20">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t20"><data value='CalculationType'>CalculationType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t37">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t37"><data value='IndustryType'>IndustryType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t51">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t51"><data value='OrganizationSize'>OrganizationSize</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t59">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t59"><data value='RiskLevel'>RiskLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t69">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t69"><data value='CalculationStatus'>CalculationStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t77">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t77"><data value='Calculation'>Calculation</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t256">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t256"><data value='CalculationTemplate'>CalculationTemplate</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t333">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t333"><data value='RiskProfile'>RiskProfile</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t510">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t510"><data value='EnhancedRiskCalculation'>EnhancedRiskCalculation</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t679">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html#t679"><data value='MonteCarloParameter'>MonteCarloParameter</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html">src/cso_platform/models/calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>160</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="160 160">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t8">src/cso_platform/models/mixins.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t8"><data value='SlugMixin'>SlugMixin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t23">src/cso_platform/models/mixins.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t23"><data value='DescriptionMixin'>DescriptionMixin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t36">src/cso_platform/models/mixins.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t36"><data value='NameMixin'>NameMixin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t49">src/cso_platform/models/mixins.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t49"><data value='TitleMixin'>TitleMixin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t62">src/cso_platform/models/mixins.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t62"><data value='StatusMixin'>StatusMixin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t77">src/cso_platform/models/mixins.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html#t77"><data value='OrderMixin'>OrderMixin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html">src/cso_platform/models/mixins.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_report_py.html#t20">src/cso_platform/models/report.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_report_py.html#t20"><data value='ReportType'>ReportType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_report_py.html#t34">src/cso_platform/models/report.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_report_py.html#t34"><data value='ExportFormat'>ExportFormat</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_report_py.html#t45">src/cso_platform/models/report.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_report_py.html#t45"><data value='ReportStatus'>ReportStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_report_py.html#t55">src/cso_platform/models/report.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_report_py.html#t55"><data value='ReportTemplate'>ReportTemplate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_report_py.html#t129">src/cso_platform/models/report.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_report_py.html#t129"><data value='Report'>Report</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_report_py.html#t208">src/cso_platform/models/report.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_report_py.html#t208"><data value='ReportExport'>ReportExport</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_report_py.html#t280">src/cso_platform/models/report.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_report_py.html#t280"><data value='BrandConfiguration'>BrandConfiguration</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_report_py.html">src/cso_platform/models/report.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_report_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>83</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="83 83">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_roi_calculation_py.html#t16">src/cso_platform/models/roi_calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_roi_calculation_py.html#t16"><data value='ROICalculation'>ROICalculation</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_roi_calculation_py.html">src/cso_platform/models/roi_calculation.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_roi_calculation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html#t19">src/cso_platform/models/user.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html#t19"><data value='UserRole'>UserRole</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html#t30">src/cso_platform/models/user.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html#t30"><data value='User'>User</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html">src/cso_platform/models/user.py</a></td>
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>53</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="53 53">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026___init___py.html">src/cso_platform/schemas/__init__.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_base_py.html#t13">src/cso_platform/schemas/base.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_base_py.html#t13"><data value='TimestampSchema'>TimestampSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_base_py.html#t31">src/cso_platform/schemas/base.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_base_py.html#t31"><data value='BaseSchema'>BaseSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_base_py.html#t47">src/cso_platform/schemas/base.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_base_py.html#t47"><data value='PaginationParams'>PaginationParams</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_base_py.html#t75">src/cso_platform/schemas/base.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_base_py.html#t75"><data value='PaginatedResponse'>PaginatedResponse</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_base_py.html#t103">src/cso_platform/schemas/base.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_base_py.html#t103"><data value='ErrorResponse'>ErrorResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_base_py.html#t111">src/cso_platform/schemas/base.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_base_py.html#t111"><data value='SuccessResponse'>SuccessResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_base_py.html">src/cso_platform/schemas/base.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t17">src/cso_platform/schemas/calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t17"><data value='CalculationBase'>CalculationBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t27">src/cso_platform/schemas/calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t27"><data value='CalculationCreate'>CalculationCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t33">src/cso_platform/schemas/calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t33"><data value='CalculationUpdate'>CalculationUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t43">src/cso_platform/schemas/calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t43"><data value='CalculationInDB'>CalculationInDB</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t62">src/cso_platform/schemas/calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t62"><data value='CalculationResponse'>CalculationResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t68">src/cso_platform/schemas/calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t68"><data value='CalculationSummary'>CalculationSummary</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t84">src/cso_platform/schemas/calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t84"><data value='CalculationExecute'>CalculationExecute</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t91">src/cso_platform/schemas/calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t91"><data value='CalculationResults'>CalculationResults</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t103">src/cso_platform/schemas/calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t103"><data value='CalculationShare'>CalculationShare</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t110">src/cso_platform/schemas/calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t110"><data value='CalculationRevision'>CalculationRevision</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t118">src/cso_platform/schemas/calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t118"><data value='CalculationTemplateBase'>CalculationTemplateBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t129">src/cso_platform/schemas/calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t129"><data value='CalculationTemplateCreate'>CalculationTemplateCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t135">src/cso_platform/schemas/calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t135"><data value='CalculationTemplateUpdate'>CalculationTemplateUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t146">src/cso_platform/schemas/calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t146"><data value='CalculationTemplateInDB'>CalculationTemplateInDB</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t155">src/cso_platform/schemas/calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t155"><data value='CalculationTemplateResponse'>CalculationTemplateResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t161">src/cso_platform/schemas/calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html#t161"><data value='CalculationTemplateUse'>CalculationTemplateUse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html">src/cso_platform/schemas/calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>93</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="93 93">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t17">src/cso_platform/schemas/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t17"><data value='IndustryTypeSchema'>IndustryTypeSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t31">src/cso_platform/schemas/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t31"><data value='OrganizationSizeSchema'>OrganizationSizeSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t39">src/cso_platform/schemas/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t39"><data value='RiskLevelSchema'>RiskLevelSchema</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t49">src/cso_platform/schemas/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t49"><data value='RiskProfileCreate'>RiskProfileCreate</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t95">src/cso_platform/schemas/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t95"><data value='RiskProfileUpdate'>RiskProfileUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t114">src/cso_platform/schemas/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t114"><data value='RiskProfileResponse'>RiskProfileResponse</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t149">src/cso_platform/schemas/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t149"><data value='MonteCarloParameterCreate'>MonteCarloParameterCreate</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t195">src/cso_platform/schemas/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t195"><data value='MonteCarloParameterResponse'>MonteCarloParameterResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t214">src/cso_platform/schemas/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t214"><data value='EnhancedRiskCalculationCreate'>EnhancedRiskCalculationCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t226">src/cso_platform/schemas/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t226"><data value='ConfidenceIntervals'>ConfidenceIntervals</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t236">src/cso_platform/schemas/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t236"><data value='ValueAtRiskResults'>ValueAtRiskResults</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t244">src/cso_platform/schemas/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t244"><data value='SensitivityAnalysisResults'>SensitivityAnalysisResults</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t252">src/cso_platform/schemas/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t252"><data value='EnhancedRiskCalculationResponse'>EnhancedRiskCalculationResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t291">src/cso_platform/schemas/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html#t291"><data value='EnhancedCalculationSummary'>EnhancedCalculationSummary</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html">src/cso_platform/schemas/enhanced_risk.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>168</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="168 168">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t17">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t17"><data value='ReportTemplateBase'>ReportTemplateBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t32">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t32"><data value='ReportTemplateCreate'>ReportTemplateCreate</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t44">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t44"><data value='ReportTemplateUpdate'>ReportTemplateUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t55">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t55"><data value='ReportTemplateInDB'>ReportTemplateInDB</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t63">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t63"><data value='ReportTemplateResponse'>ReportTemplateResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t69">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t69"><data value='ReportBase'>ReportBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t78">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t78"><data value='ReportCreate'>ReportCreate</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t92">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t92"><data value='ReportUpdate'>ReportUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t100">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t100"><data value='ReportInDB'>ReportInDB</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t112">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t112"><data value='ReportResponse'>ReportResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t118">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t118"><data value='ReportExportBase'>ReportExportBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t126">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t126"><data value='ReportExportCreate'>ReportExportCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t132">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t132"><data value='ReportExportInDB'>ReportExportInDB</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t144">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t144"><data value='ReportExportResponse'>ReportExportResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t150">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t150"><data value='BrandConfigurationBase'>BrandConfigurationBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t164">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t164"><data value='BrandConfigurationCreate'>BrandConfigurationCreate</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t180">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t180"><data value='BrandConfigurationUpdate'>BrandConfigurationUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t194">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t194"><data value='BrandConfigurationInDB'>BrandConfigurationInDB</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t201">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t201"><data value='BrandConfigurationResponse'>BrandConfigurationResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t207">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t207"><data value='ReportGenerationRequest'>ReportGenerationRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t221">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t221"><data value='ReportGenerationResponse'>ReportGenerationResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t230">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html#t230"><data value='ReportDownloadResponse'>ReportDownloadResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html">src/cso_platform/schemas/report.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>112</td>
                <td>112</td>
                <td>0</td>
                <td class="right" data-ratio="0 112">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html#t15">src/cso_platform/schemas/roi_calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html#t15"><data value='ROICalculationBase'>ROICalculationBase</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html#t84">src/cso_platform/schemas/roi_calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html#t84"><data value='ROICalculationCreate'>ROICalculationCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html#t89">src/cso_platform/schemas/roi_calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html#t89"><data value='ROICalculationUpdate'>ROICalculationUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html#t142">src/cso_platform/schemas/roi_calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html#t142"><data value='ROICalculationResults'>ROICalculationResults</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html#t187">src/cso_platform/schemas/roi_calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html#t187"><data value='ROICalculationResponse'>ROICalculationResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html#t194">src/cso_platform/schemas/roi_calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html#t194"><data value='ROICalculationListResponse'>ROICalculationListResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html#t204">src/cso_platform/schemas/roi_calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html#t204"><data value='QuickROICalculationRequest'>QuickROICalculationRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html#t244">src/cso_platform/schemas/roi_calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html#t244"><data value='QuickROICalculationResponse'>QuickROICalculationResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html">src/cso_platform/schemas/roi_calculation.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>53</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="53 53">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t16">src/cso_platform/schemas/user.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t16"><data value='UserBase'>UserBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t36">src/cso_platform/schemas/user.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t36"><data value='UserCreate'>UserCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t44">src/cso_platform/schemas/user.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t44"><data value='UserUpdate'>UserUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t64">src/cso_platform/schemas/user.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t64"><data value='UserInDB'>UserInDB</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t77">src/cso_platform/schemas/user.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t77"><data value='UserResponse'>UserResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t81">src/cso_platform/schemas/user.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t81"><data value='Config'>UserResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t85">src/cso_platform/schemas/user.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t85"><data value='UserLogin'>UserLogin</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t92">src/cso_platform/schemas/user.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t92"><data value='UserRegistration'>UserRegistration</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t104">src/cso_platform/schemas/user.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t104"><data value='Token'>Token</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t112">src/cso_platform/schemas/user.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t112"><data value='TokenData'>TokenData</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t119">src/cso_platform/schemas/user.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t119"><data value='PasswordChange'>PasswordChange</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t133">src/cso_platform/schemas/user.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t133"><data value='PasswordReset'>PasswordReset</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t139">src/cso_platform/schemas/user.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t139"><data value='PasswordResetConfirm'>PasswordResetConfirm</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t153">src/cso_platform/schemas/user.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t153"><data value='EmailVerification'>EmailVerification</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t159">src/cso_platform/schemas/user.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html#t159"><data value='UserProfile'>UserProfile</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html">src/cso_platform/schemas/user.py</a></td>
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>83</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="83 83">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70___init___py.html">src/cso_platform/services/__init__.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t32">src/cso_platform/services/enhanced_risk_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html#t32"><data value='EnhancedRiskService'>EnhancedRiskService</data></a></td>
                <td>121</td>
                <td>121</td>
                <td>0</td>
                <td class="right" data-ratio="0 121">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html">src/cso_platform/services/enhanced_risk_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="12 27">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_roi_calculation_py.html#t21">src/cso_platform/services/roi_calculation.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_roi_calculation_py.html#t21"><data value='ROICalculationService'>ROICalculationService</data></a></td>
                <td>56</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_roi_calculation_py.html">src/cso_platform/services/roi_calculation.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_roi_calculation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t18">src/cso_platform/services/user.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html#t18"><data value='UserService'>UserService</data></a></td>
                <td>70</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="0 70">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html">src/cso_platform/services/user.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t23">src/cso_platform/services/user_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html#t23"><data value='UserService'>UserService</data></a></td>
                <td>77</td>
                <td>77</td>
                <td>0</td>
                <td class="right" data-ratio="0 77">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html">src/cso_platform/services/user_service.py</a></td>
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="8 23">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971___init___py.html">src/cso_platform/utils/__init__.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="1 2">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t17">src/cso_platform/utils/monte_carlo.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t17"><data value='DistributionParameter'>DistributionParameter</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t26">src/cso_platform/utils/monte_carlo.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t26"><data value='MonteCarloEngine'>MonteCarloEngine</data></a></td>
                <td>123</td>
                <td>123</td>
                <td>0</td>
                <td class="right" data-ratio="0 123">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t286">src/cso_platform/utils/monte_carlo.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html#t286"><data value='RiskCalculationFunction'>RiskCalculationFunction</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html">src/cso_platform/utils/monte_carlo.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t29">src/cso_platform/utils/service_url_manager.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html#t29"><data value='ServiceURLManager'>ServiceURLManager</data></a></td>
                <td>70</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="0 70">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html">src/cso_platform/utils/service_url_manager.py</a></td>
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="6 32">19%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>2476</td>
                <td>1527</td>
                <td>0</td>
                <td class="right" data-ratio="949 2476">38%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-20 16:07 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
