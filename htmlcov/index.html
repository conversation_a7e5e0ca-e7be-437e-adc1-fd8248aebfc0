<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">38%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-20 16:07 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_a8e3d2a25284f10c___init___py.html">src/cso_platform/__init__.py</a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_7d55aa91cc0e4b64___init___py.html">src/cso_platform/api/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d6d90c030a097e00___init___py.html">src/cso_platform/api/middleware/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ff330425635f025___init___py.html">src/cso_platform/api/v1/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ff330425635f025_dependencies_py.html">src/cso_platform/api/v1/dependencies.py</a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c___init___py.html">src/cso_platform/api/v1/endpoints/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_auth_py.html">src/cso_platform/api/v1/endpoints/auth.py</a></td>
                <td>112</td>
                <td>112</td>
                <td>0</td>
                <td class="right" data-ratio="0 112">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_enhanced_risk_py.html">src/cso_platform/api/v1/endpoints/enhanced_risk.py</a></td>
                <td>163</td>
                <td>163</td>
                <td>0</td>
                <td class="right" data-ratio="0 163">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_roi_calculations_py.html">src/cso_platform/api/v1/endpoints/roi_calculations.py</a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_f0c218527f5a391c_users_py.html">src/cso_platform/api/v1/endpoints/users.py</a></td>
                <td>103</td>
                <td>103</td>
                <td>0</td>
                <td class="right" data-ratio="0 103">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0ff330425635f025_router_py.html">src/cso_platform/api/v1/router.py</a></td>
                <td>24</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="2 24">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9___init___py.html">src/cso_platform/core/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_auth_py.html">src/cso_platform/core/auth.py</a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_config_py.html">src/cso_platform/core/config.py</a></td>
                <td>77</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="59 77">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_database_py.html">src/cso_platform/core/database.py</a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_database_manager_py.html">src/cso_platform/core/database_manager.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_exceptions_py.html">src/cso_platform/core/exceptions.py</a></td>
                <td>60</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="37 60">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e3e910735ada1cf9_security_py.html">src/cso_platform/core/security.py</a></td>
                <td>48</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="5 48">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a8e3d2a25284f10c_main_py.html">src/cso_platform/main.py</a></td>
                <td>28</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="7 28">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658___init___py.html">src/cso_platform/models/__init__.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_base_py.html">src/cso_platform/models/base.py</a></td>
                <td>39</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="33 39">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_calculation_py.html">src/cso_platform/models/calculation.py</a></td>
                <td>204</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="160 204">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_mixins_py.html">src/cso_platform/models/mixins.py</a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_report_py.html">src/cso_platform/models/report.py</a></td>
                <td>83</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="83 83">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_roi_calculation_py.html">src/cso_platform/models/roi_calculation.py</a></td>
                <td>31</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="28 31">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_655d59a19daa5658_user_py.html">src/cso_platform/models/user.py</a></td>
                <td>65</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="53 65">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026___init___py.html">src/cso_platform/schemas/__init__.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_base_py.html">src/cso_platform/schemas/base.py</a></td>
                <td>34</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="29 34">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_calculation_py.html">src/cso_platform/schemas/calculation.py</a></td>
                <td>93</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="93 93">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_enhanced_risk_py.html">src/cso_platform/schemas/enhanced_risk.py</a></td>
                <td>195</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="168 195">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_report_py.html">src/cso_platform/schemas/report.py</a></td>
                <td>129</td>
                <td>129</td>
                <td>0</td>
                <td class="right" data-ratio="0 129">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_roi_calculation_py.html">src/cso_platform/schemas/roi_calculation.py</a></td>
                <td>59</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="53 59">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e8cb0c54856c2026_user_py.html">src/cso_platform/schemas/user.py</a></td>
                <td>92</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="83 92">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70___init___py.html">src/cso_platform/services/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_enhanced_risk_service_py.html">src/cso_platform/services/enhanced_risk_service.py</a></td>
                <td>148</td>
                <td>136</td>
                <td>0</td>
                <td class="right" data-ratio="12 148">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_roi_calculation_py.html">src/cso_platform/services/roi_calculation.py</a></td>
                <td>71</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="15 71">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_py.html">src/cso_platform/services/user.py</a></td>
                <td>89</td>
                <td>89</td>
                <td>0</td>
                <td class="right" data-ratio="0 89">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_49c5e7e7acd43c70_user_service_py.html">src/cso_platform/services/user_service.py</a></td>
                <td>100</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="8 100">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971___init___py.html">src/cso_platform/utils/__init__.py</a></td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="1 2">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_monte_carlo_py.html">src/cso_platform/utils/monte_carlo.py</a></td>
                <td>164</td>
                <td>164</td>
                <td>0</td>
                <td class="right" data-ratio="0 164">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cf6ca5f0603ea971_service_url_manager_py.html">src/cso_platform/utils/service_url_manager.py</a></td>
                <td>102</td>
                <td>96</td>
                <td>0</td>
                <td class="right" data-ratio="6 102">6%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>2476</td>
                <td>1527</td>
                <td>0</td>
                <td class="right" data-ratio="949 2476">38%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-20 16:07 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_cf6ca5f0603ea971_service_url_manager_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_a8e3d2a25284f10c___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
